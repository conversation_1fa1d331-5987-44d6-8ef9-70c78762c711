package com.wms.event;

import com.wms.entity.Manifest;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDate;

/**
 * Event fired when a manifest's delivery date is changed
 * This triggers immediate status checks and WebSocket notifications
 */
public class DeliveryDateChangedEvent extends ApplicationEvent {
    
    private final Manifest manifest;
    private final LocalDate oldDeliveryDate;
    private final LocalDate newDeliveryDate;
    private final String changedBy;
    
    public DeliveryDateChangedEvent(Object source, Manifest manifest, 
                                   LocalDate oldDeliveryDate, 
                                   LocalDate newDeliveryDate,
                                   String changedBy) {
        super(source);
        this.manifest = manifest;
        this.oldDeliveryDate = oldDeliveryDate;
        this.newDeliveryDate = newDeliveryDate;
        this.changedBy = changedBy;
    }
    
    public Manifest getManifest() {
        return manifest;
    }
    
    public LocalDate getOldDeliveryDate() {
        return oldDeliveryDate;
    }
    
    public LocalDate getNewDeliveryDate() {
        return newDeliveryDate;
    }
    
    public String getChangedBy() {
        return changedBy;
    }
    
    @Override
    public String toString() {
        return String.format("DeliveryDateChangedEvent{manifest=%s, oldDate=%s, newDate=%s, changedBy=%s}", 
                           manifest.getTrackingNo(), oldDeliveryDate, newDeliveryDate, changedBy);
    }
}
