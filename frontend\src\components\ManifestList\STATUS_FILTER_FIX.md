# Status Filter Fix Documentation

## Issue Description

When applying status filters using the status chips in the ManifestList component, the numbers shown beside each status chip would be set to zero for all statuses except the currently filtered one.

### Example of the Problem

If you had manifests with the following statuses:
- CREATED: 5 manifests
- INBOUNDED_TO_WAREHOUSE: 3 manifests  
- READY_TO_DELIVER: 2 manifests
- DELIVERED: 10 manifests

When you clicked on the "CREATED" status chip to filter, the chips would show:
- CREATED (5) ✓ - Correct
- INBOUNDED_TO_WAREHOUSE (0) ❌ - Should show (3)
- READY_TO_DELIVER (0) ❌ - Should show (2)  
- DELIVERED (0) ❌ - Should show (10)

## Root Cause

The issue was in the `ManifestList.tsx` component where the totals calculation was using `filteredManifests` for both the status counts and other totals:

```typescript
// OLD (INCORRECT) CODE
const totals = useMemo(() => {
  return calculateTotals(filteredManifests); // ❌ Using filtered manifests for status counts
}, [filteredManifests]);

const calculateTotals = (manifests: Manifest[]) => {
  const result = manifests.reduce((totals, manifest) => {
    // ❌ This counts only manifests that passed the filter
    const status = manifest.status;
    totals.byStatus[status] = (totals.byStatus[status] || 0) + 1;
    // ... other calculations
  }, {
    // ...
    byStatus: {} as Record<ManifestStatus, number>
  });
  return result;
};
```

When a status filter was applied, `filteredManifests` only contained manifests with the selected status, so the status counts would only reflect those filtered manifests.

## Solution

The fix separates the calculation of status counts from other totals:

1. **Status counts** are calculated from the **original unfiltered manifests** array
2. **Other totals** (pieces, CBM, etc.) are calculated from the **filtered manifests** array

```typescript
// NEW (CORRECT) CODE
const calculateTotals = (manifests: Manifest[]) => {
  // Only calculate non-status totals here
  const result = manifests.reduce((totals, manifest) => {
    return {
      totalCBM: totals.totalCBM + (manifest.cbm || 0),
      totalPallets: totals.totalPallets + (manifest.actualPalletsCount || 0),
      totalPieces: totals.totalPieces + (manifest.pieces || 0),
      totalInboundPieces: totals.totalInboundPieces + (manifest.inboundPieces || 0),
    };
  }, {
    totalCBM: 0,
    totalPallets: 0,
    totalPieces: 0,
    totalInboundPieces: 0,
  });
  return result;
};

// Separate function for status counts
const calculateStatusCounts = (manifests: Manifest[]) => {
  return manifests.reduce((acc, manifest) => {
    const status = manifest.status;
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {} as Record<ManifestStatus, number>);
};

// Combine both calculations
const totals = useMemo(() => {
  const filteredTotals = calculateTotals(filteredManifests); // ✅ Filtered for totals
  const statusCounts = calculateStatusCounts(manifests); // ✅ Original for status counts
  
  return {
    ...filteredTotals,
    byStatus: statusCounts
  };
}, [filteredManifests, manifests]); // ✅ Depends on both arrays
```

## Why This Approach is Correct

1. **Status counts should show the total available manifests** in each status, regardless of current filters. This helps users understand what data is available and make informed filtering decisions.

2. **Other totals should reflect the filtered data** because they represent aggregated values (pieces, CBM, etc.) for the currently visible/selected manifests.

3. **This matches the pattern used in ContainerList** which correctly calculates status counts from the original containers array.

## Files Modified

- `frontend/src/components/ManifestList/ManifestList.tsx` - Fixed the totals calculation logic

## Testing

The fix can be tested by:

1. Loading a container with manifests in multiple statuses
2. Applying a status filter by clicking on a status chip
3. Verifying that other status chips still show their original counts (not zero)
4. Verifying that the totals (pieces, CBM) reflect only the filtered manifests

## Related Components

This fix applies to any component that uses the ManifestList component:
- ContainerDetailTest.tsx
- ManifestListTest.tsx

The ContainerList component already implements this pattern correctly and was not affected by this issue.
