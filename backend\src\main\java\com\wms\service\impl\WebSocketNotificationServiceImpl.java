package com.wms.service.impl;

import com.wms.entity.Manifest;
import com.wms.entity.ManifestStatus;
import com.wms.service.WebSocketNotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * Implementation of WebSocket notification service for real-time updates
 */
@Service
public class WebSocketNotificationServiceImpl implements WebSocketNotificationService {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketNotificationServiceImpl.class);
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public void broadcastManifestStatusUpdate(Manifest manifest, ManifestStatus oldStatus, ManifestStatus newStatus, String reason) {
        try {
            Map<String, Object> update = createManifestStatusUpdate(manifest, oldStatus, newStatus, reason);
            
            // Broadcast to all clients subscribed to manifest status updates
            messagingTemplate.convertAndSend("/topic/manifest-status", update);
            
            // Also send to container-specific topic if manifest has container
            if (manifest.getContainer() != null && manifest.getContainer().getContainerNo() != null) {
                messagingTemplate.convertAndSend("/topic/container/" + manifest.getContainer().getContainerNo() + "/manifests", update);
            }
            
            logger.info("Broadcasted manifest status update via WebSocket: {} {} -> {} ({})", 
                       manifest.getTrackingNo(), oldStatus, newStatus, reason);
                       
        } catch (Exception e) {
            logger.error("Error broadcasting manifest status update via WebSocket: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public void sendManifestStatusUpdateToUser(String username, Manifest manifest, ManifestStatus oldStatus, ManifestStatus newStatus, String reason) {
        try {
            Map<String, Object> update = createManifestStatusUpdate(manifest, oldStatus, newStatus, reason);
            
            // Send to specific user
            messagingTemplate.convertAndSendToUser(username, "/queue/manifest-status", update);
            
            logger.info("Sent manifest status update to user {} via WebSocket: {} {} -> {} ({})", 
                       username, manifest.getTrackingNo(), oldStatus, newStatus, reason);
                       
        } catch (Exception e) {
            logger.error("Error sending manifest status update to user {} via WebSocket: {}", username, e.getMessage(), e);
        }
    }
    
    @Override
    public void broadcastContainerManifestUpdate(String containerNo, Manifest manifest, ManifestStatus oldStatus, ManifestStatus newStatus, String reason) {
        try {
            Map<String, Object> update = createManifestStatusUpdate(manifest, oldStatus, newStatus, reason);
            
            // Send to container-specific topic
            messagingTemplate.convertAndSend("/topic/container/" + containerNo + "/manifests", update);
            
            logger.info("Broadcasted container manifest update via WebSocket: Container {} - {} {} -> {} ({})", 
                       containerNo, manifest.getTrackingNo(), oldStatus, newStatus, reason);
                       
        } catch (Exception e) {
            logger.error("Error broadcasting container manifest update via WebSocket: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public void broadcastSystemNotification(String message, String type) {
        try {
            Map<String, Object> notification = new HashMap<>();
            notification.put("type", "system_notification");
            notification.put("message", message);
            notification.put("notificationType", type);
            notification.put("timestamp", LocalDateTime.now().format(TIMESTAMP_FORMATTER));
            
            messagingTemplate.convertAndSend("/topic/system-notifications", notification);
            
            logger.info("Broadcasted system notification via WebSocket: {} ({})", message, type);
            
        } catch (Exception e) {
            logger.error("Error broadcasting system notification via WebSocket: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Create a standardized manifest status update message
     */
    private Map<String, Object> createManifestStatusUpdate(Manifest manifest, ManifestStatus oldStatus, ManifestStatus newStatus, String reason) {
        Map<String, Object> update = new HashMap<>();
        
        // Basic update information
        update.put("type", "manifest_status_update");
        update.put("timestamp", LocalDateTime.now().format(TIMESTAMP_FORMATTER));
        update.put("reason", reason);
        
        // Status change information
        update.put("oldStatus", oldStatus != null ? oldStatus.toString() : null);
        update.put("newStatus", newStatus.toString());
        
        // Manifest information
        Map<String, Object> manifestInfo = new HashMap<>();
        manifestInfo.put("trackingNo", manifest.getTrackingNo());
        manifestInfo.put("status", manifest.getStatus().toString());
        manifestInfo.put("pieces", manifest.getPieces());
        manifestInfo.put("deliveryDate", manifest.getDeliveryDate() != null ? manifest.getDeliveryDate().toString() : null);
        manifestInfo.put("actualPalletsCount", manifest.getActualPalletsCount());
        
        // Container information if available
        if (manifest.getContainer() != null) {
            Map<String, Object> containerInfo = new HashMap<>();
            containerInfo.put("containerNo", manifest.getContainer().getContainerNo());
            containerInfo.put("status", manifest.getContainer().getStatus().toString());
            manifestInfo.put("container", containerInfo);
        }
        
        update.put("manifest", manifestInfo);
        
        return update;
    }
}
