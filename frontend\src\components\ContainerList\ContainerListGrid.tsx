import React, { useState, useMemo, useCallback } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Tooltip,
  Chip,
  useTheme,
  alpha,
  Alert,
  Checkbox
} from '@mui/material';
import {
  DataGrid,
  GridColDef,
  GridRenderCellParams,
  GridToolbar
} from '@mui/x-data-grid';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import PersonIcon from '@mui/icons-material/Person';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import { Container, ContainerStatus } from '../../types/Container';
import { formatDateString } from '../../utils/dateUtils';
import { useNavigate } from 'react-router-dom';

interface ContainerListGridProps {
  containers: Container[];
  selectedContainers: Container[];
  onSelectionChange: (containers: Container[]) => void;
  onContainerDelete?: (containerNo: string) => void;
  loading: boolean;
}

const ContainerListGrid: React.FC<ContainerListGridProps> = ({
  containers = [],
  selectedContainers = [],
  onSelectionChange,
  onContainerDelete,
  loading = false
}) => {
  const theme = useTheme();
  const navigate = useNavigate();

  // Note: Selection functionality temporarily disabled due to DataGrid compatibility issues
  // Will be re-implemented with proper selection model handling

  // Get container status colors and styling
  const getStatusChipProps = (status: ContainerStatus) => {
    const baseProps = {
      size: 'small' as const,
      sx: { 
        fontSize: '0.7rem',
        fontWeight: 600,
        minWidth: '80px',
        height: '24px'
      }
    };

    switch (status) {
      case ContainerStatus.CREATED:
        return {
          ...baseProps,
          label: 'Created',
          color: 'default' as const,
          sx: { ...baseProps.sx, bgcolor: alpha(theme.palette.grey[500], 0.1) }
        };
      case ContainerStatus.CONFIRMED:
        return {
          ...baseProps,
          label: 'Confirmed',
          color: 'info' as const
        };
      case ContainerStatus.ARRIVED:
        return {
          ...baseProps,
          label: 'Arrived',
          color: 'primary' as const
        };
      case ContainerStatus.UNSTUFFING:
        return {
          ...baseProps,
          label: 'Unstuffing',
          color: 'warning' as const
        };
      case ContainerStatus.UNSTUFF_COMPLETED:
        return {
          ...baseProps,
          label: 'Unstuff Completed',
          color: 'success' as const
        };
      case ContainerStatus.READY_TO_PULL_OUT:
        return {
          ...baseProps,
          label: 'Ready to Pull Out',
          color: 'secondary' as const
        };
      case ContainerStatus.PULLED_OUT:
        return {
          ...baseProps,
          label: 'Pulled Out',
          color: 'success' as const
        };
      case ContainerStatus.RED_SEAL:
        return {
          ...baseProps,
          label: 'Red Seal',
          color: 'error' as const
        };
      default:
        return {
          ...baseProps,
          label: (status as string).replace(/_/g, ' '),
          color: 'default' as const
        };
    }
  };

  // Handle individual row selection
  const handleRowSelect = useCallback((container: Container, isSelected: boolean) => {
    if (isSelected) {
      // Add to selection
      const newSelection = [...selectedContainers, container];
      onSelectionChange(newSelection);
    } else {
      // Remove from selection
      const newSelection = selectedContainers.filter(c => c.containerNo !== container.containerNo);
      onSelectionChange(newSelection);
    }
  }, [selectedContainers, onSelectionChange]);

  // Handle select all
  const handleSelectAll = useCallback((isSelected: boolean) => {
    if (isSelected) {
      onSelectionChange([...containers]);
    } else {
      onSelectionChange([]);
    }
  }, [containers, onSelectionChange]);

  // Check if a container is selected
  const isContainerSelected = useCallback((containerNo: string) => {
    return selectedContainers.some(c => c.containerNo === containerNo);
  }, [selectedContainers]);

  // Check if all containers are selected
  const isAllSelected = containers.length > 0 && selectedContainers.length === containers.length;
  const isIndeterminate = selectedContainers.length > 0 && selectedContainers.length < containers.length;

  // Handle container view/edit
  const handleContainerView = (containerNo: string) => {
    navigate(`/containers/${containerNo}`);
  };

  // Handle container delete
  const handleDelete = (containerNo: string) => {
    if (onContainerDelete) {
      onContainerDelete(containerNo);
    }
  };

  // Define columns
  const columns: GridColDef[] = useMemo(() => [
    {
      field: 'select',
      headerName: '',
      width: 50,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      renderHeader: () => (
        <Checkbox
          checked={isAllSelected}
          indeterminate={isIndeterminate}
          onChange={(e) => handleSelectAll(e.target.checked)}
          size="small"
        />
      ),
      renderCell: (params: GridRenderCellParams) => (
        <Checkbox
          checked={isContainerSelected(params.row.containerNo)}
          onChange={(e) => handleRowSelect(params.row, e.target.checked)}
          size="small"
        />
      )
    },
    {
      field: 'containerNo',
      headerName: 'Container No',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DirectionsBoatIcon fontSize="small" color="primary" />
          <Typography
            variant="body2"
            sx={{
              fontWeight: 600,
              color: 'primary.main',
              cursor: 'pointer',
              '&:hover': { textDecoration: 'underline' }
            }}
            onClick={() => handleContainerView(params.value)}
          >
            {params.value}
          </Typography>
        </Box>
      )
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params: GridRenderCellParams) => {
        const chipProps = getStatusChipProps(params.value);
        return <Chip {...chipProps} />;
      }
    },
    {
      field: 'client',
      headerName: 'Client',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PersonIcon fontSize="small" color="action" />
          <Typography variant="body2">
            {params.value?.companyName || params.value?.username || '-'}
          </Typography>
        </Box>
      )
    },
    {
      field: 'truckNo',
      headerName: 'Truck No',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LocalShippingIcon fontSize="small" color="action" />
          <Typography variant="body2">
            {params.value || '-'}
          </Typography>
        </Box>
      )
    },
    {
      field: 'vesselVoyageNo',
      headerName: 'Vessel/Voyage',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value || '-'}
        </Typography>
      )
    },
    {
      field: 'manifestQuantity',
      headerName: 'Manifest Qty',
      width: 120,
      type: 'number',
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontWeight: 500 }}>
          {params.value || 0}
        </Typography>
      )
    },
    {
      field: 'etaRequestedDate',
      headerName: 'ETA Requested',
      width: 130,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CalendarTodayIcon fontSize="small" color="action" />
          <Typography variant="body2">
            {params.value ? formatDateString(params.value) : '-'}
          </Typography>
        </Box>
      )
    },
    {
      field: 'etaAllocated',
      headerName: 'ETA Allocated',
      width: 130,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value ? formatDateString(params.value) : '-'}
        </Typography>
      )
    },
    {
      field: 'arrivalDate',
      headerName: 'Arrival Date',
      width: 130,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value ? formatDateString(params.value) : '-'}
        </Typography>
      )
    },
    {
      field: 'loadingBay',
      headerName: 'Loading Bay',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <WarehouseIcon fontSize="small" color="action" />
          <Typography variant="body2">
            {params.value || '-'}
          </Typography>
        </Box>
      )
    },
    {
      field: 'unstuffDate',
      headerName: 'Unstuff Date',
      width: 130,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value ? formatDateString(params.value) : '-'}
        </Typography>
      )
    },
    {
      field: 'unstuffTeam',
      headerName: 'Unstuff Team',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value || '-'}
        </Typography>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <Tooltip title="View container details">
            <IconButton
              size="small"
              onClick={() => handleContainerView(params.row.containerNo)}
              sx={{ color: 'primary.main' }}
            >
              <VisibilityIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Delete container">
            <IconButton
              size="small"
              onClick={() => handleDelete(params.row.containerNo)}
              sx={{ color: 'error.main' }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      )
    }
  ], [navigate, onContainerDelete, isAllSelected, isIndeterminate, handleSelectAll, handleRowSelect, isContainerSelected]);

  // Note: We're using custom selection instead of DataGrid's built-in selection

  // Custom footer component to avoid DataGrid footer issues
  const CustomFooter = () => (
    <Box sx={{
      p: 1,
      borderTop: `1px solid ${theme.palette.divider}`,
      backgroundColor: alpha(theme.palette.grey[50], 0.5),
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }}>
      <Typography variant="body2" color="text.secondary">
        {containers.length} containers total
      </Typography>
      {selectedContainers.length > 0 && (
        <Typography variant="body2" color="primary">
          {selectedContainers.length} selected
        </Typography>
      )}
    </Box>
  );

  // Early return if containers is not properly initialized
  if (!Array.isArray(containers)) {
    return (
      <Paper sx={{
        height: '100%',
        width: '100%',
        borderRadius: 0,
        borderBottomLeftRadius: '8px',
        borderBottomRightRadius: '8px',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 3
      }}>
        <Typography variant="body2" color="text.secondary">
          Loading container data...
        </Typography>
      </Paper>
    );
  }

  // Error fallback component
  const ErrorFallback = () => (
    <Box sx={{
      height: '100%',
      width: '100%',
      p: 3,
      textAlign: 'center'
    }}>
      <Alert severity="error">
        <Typography variant="h6" gutterBottom>
          Container Grid Error
        </Typography>
        <Typography variant="body2">
          There was an error loading the container grid. Please refresh the page or try again later.
        </Typography>
      </Alert>
    </Box>
  );

  try {
    return (
      <Box sx={{
        height: '100%',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative'
      }}>
        <DataGrid
          rows={containers || []}
          columns={columns}
          getRowId={(row) => row.containerNo}
          loading={loading}
          slots={{
            toolbar: GridToolbar
          }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
              quickFilterProps: { debounceMs: 500 }
            }
          }}
          pageSizeOptions={[10, 25, 50, 100]}
          density="compact"
          autoHeight={false}
          disableVirtualization={false}
          hideFooter={true}
          initialState={{
            pagination: {
              paginationModel: { pageSize: 25, page: 0 }
            }
          }}
          sx={{
            border: 'none',
            height: '100%',
            minHeight: '400px',
            '& .MuiDataGrid-main': {
              borderRadius: 0
            },
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: alpha(theme.palette.primary.main, 0.05),
              borderBottom: `1px solid ${theme.palette.divider}`,
              '& .MuiDataGrid-columnHeader': {
                fontWeight: 600,
                fontSize: '0.75rem'
              }
            },
            '& .MuiDataGrid-row': {
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.04)
              },
              '&.Mui-selected': {
                backgroundColor: alpha(theme.palette.primary.main, 0.08),
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.12)
                }
              }
            },
            '& .MuiDataGrid-cell': {
              fontSize: '0.75rem',
              borderBottom: `1px solid ${alpha(theme.palette.divider, 0.5)}`
            },
            '& .MuiDataGrid-footerContainer': {
              borderTop: `1px solid ${theme.palette.divider}`,
              backgroundColor: alpha(theme.palette.grey[50], 0.5)
            }
          }}
        />
        <CustomFooter />
      </Box>
    );
  } catch (error) {
    console.error('DataGrid render error:', error);
    return <ErrorFallback />;
  }
};

export default ContainerListGrid;
