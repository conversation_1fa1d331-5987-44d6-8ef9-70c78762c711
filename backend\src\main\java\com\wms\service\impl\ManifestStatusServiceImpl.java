package com.wms.service.impl;

import com.wms.entity.Manifest;
import com.wms.entity.ManifestStatus;
import com.wms.entity.Pallet;
import com.wms.repository.ManifestRepository;
import com.wms.repository.PalletRepository;
import com.wms.service.ManifestStatusService;
import com.wms.service.ManifestTrackingLogService;
import com.wms.service.WebSocketNotificationService;
import com.wms.event.DeliveryDateChangedEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class ManifestStatusServiceImpl implements ManifestStatusService {

    private final ManifestRepository manifestRepository;
    private final PalletRepository palletRepository;
    private final ManifestTrackingLogService trackingLogService;
    private final WebSocketNotificationService webSocketNotificationService;
    private static final Logger logger = LoggerFactory.getLogger(ManifestStatusServiceImpl.class);

    @Autowired
    public ManifestStatusServiceImpl(ManifestRepository manifestRepository,
                                    PalletRepository palletRepository,
                                    ManifestTrackingLogService trackingLogService,
                                    WebSocketNotificationService webSocketNotificationService) {
        this.manifestRepository = manifestRepository;
        this.palletRepository = palletRepository;
        this.trackingLogService = trackingLogService;
        this.webSocketNotificationService = webSocketNotificationService;
    }

    @Override
    @Transactional
    public Manifest checkAndUpdateManifestStatus(Manifest manifest) {
        return checkAndUpdateManifestStatus(manifest, true);
    }

    @Override
    @Transactional
    public Manifest checkAndUpdateManifestStatus(Manifest manifest, boolean logStatusChange) {
        if (manifest == null) {
            return null;
        }
        
        // Get all pallets for this manifest
        List<Pallet> pallets = palletRepository.findByManifestTrackingNo(manifest.getTrackingNo());
        
        // Calculate total pieces in all pallets
        int totalPiecesInPallets = pallets.stream()
                .mapToInt(Pallet::getNoOfPieces)
                .sum();
        
        // Get manifest pieces
        int manifestPieces = manifest.getPieces();
        
        logger.debug("Checking status conditions for manifest {}: manifest pieces={}, total pallet pieces={}", 
                   manifest.getTrackingNo(), manifestPieces, totalPiecesInPallets);
        
        ManifestStatus originalStatus = manifest.getStatus();
        ManifestStatus newStatus = determineStatusBasedOnPalletPieces(originalStatus, totalPiecesInPallets, manifestPieces, pallets.isEmpty(), manifest);
        
        // Only update if status has changed
        if (newStatus != originalStatus) {
            manifest.setStatus(newStatus);
            logger.info("Status change for manifest {}: {} -> {} (manifest pieces={}, total pallet pieces={})", 
                      manifest.getTrackingNo(), originalStatus, newStatus, manifestPieces, totalPiecesInPallets);
            
            // Save the manifest first
            Manifest savedManifest = manifestRepository.save(manifest);
            
            // Log the first status change
            if (logStatusChange) {
                try {
                    String currentUser = getCurrentUser();
                    trackingLogService.logStatusChange(
                        manifest.getTrackingNo(), 
                        originalStatus, 
                        savedManifest.getStatus(), 
                        currentUser, 
                        "Status auto-updated based on pallet pieces count"
                    );
                } catch (Exception e) {
                    logger.warn("Failed to log status change for {}: {}", manifest.getTrackingNo(), e.getMessage());
                }
            }
            
            // After any pallet-based status change, check delivery date conditions
            // This ensures delivery statuses are properly updated or reverted
            if (savedManifest.getDeliveryDate() != null && 
                (savedManifest.getStatus() == ManifestStatus.INBOUNDED_TO_WAREHOUSE ||
                 savedManifest.getStatus() == ManifestStatus.READY_TO_DELIVER ||
                 savedManifest.getStatus() == ManifestStatus.PENDING_DELIVER)) {
                // Check if we should update the status based on delivery date
                // This method will handle its own logging separately
                return checkAndUpdateStatusBasedOnDeliveryDate(savedManifest);
            }
            
            return savedManifest;
        } else {
            // Even if pallet-based status didn't change, we should still check delivery date conditions
            // This handles cases where pallets are added/removed but total count results in same status,
            // but delivery conditions might need re-evaluation
            if (manifest.getDeliveryDate() != null && 
                (manifest.getStatus() == ManifestStatus.INBOUNDED_TO_WAREHOUSE ||
                 manifest.getStatus() == ManifestStatus.READY_TO_DELIVER ||
                 manifest.getStatus() == ManifestStatus.PENDING_DELIVER)) {
                return checkAndUpdateStatusBasedOnDeliveryDate(manifest);
            }
            
            logger.debug("No status change required for manifest {}", manifest.getTrackingNo());
            return manifest;
        }
    }
    
    @Override
    @Transactional
    public Manifest checkAndUpdateStatusBasedOnDeliveryDate(Manifest manifest) {
        if (manifest == null || manifest.getDeliveryDate() == null) {
            return manifest;
        }
        
        // Only apply to manifests in these statuses
        if (manifest.getStatus() != ManifestStatus.INBOUNDED_TO_WAREHOUSE && 
            manifest.getStatus() != ManifestStatus.READY_TO_DELIVER && 
            manifest.getStatus() != ManifestStatus.PENDING_DELIVER) {
            return manifest;
        }
        
        ManifestStatus originalStatus = manifest.getStatus();
        
        // Compare dates only (ignore time) - use Singapore timezone explicitly
        LocalDate today = LocalDate.now(java.time.ZoneId.of("Asia/Singapore"));
        LocalDate deliveryDate = manifest.getDeliveryDate();

        // Calculate days between today and delivery date
        long diffDays = java.time.temporal.ChronoUnit.DAYS.between(today, deliveryDate);

        // Use INFO level logging so it's visible in production
        logger.info("Checking delivery date for manifest {}: today={}, deliveryDate={}, days until delivery={}",
                   manifest.getTrackingNo(), today, deliveryDate, diffDays);
        
        ManifestStatus newStatus = originalStatus;
        
        // Determine the appropriate status based on the delivery date
        if (diffDays <= 1) {
            // If delivery is today or tomorrow, set status to READY_TO_DELIVER
            newStatus = ManifestStatus.READY_TO_DELIVER;
            logger.info("Manifest {} should be READY_TO_DELIVER (delivery within 1 day)", manifest.getTrackingNo());
        } else if (diffDays >= 2) {
            // If delivery is 2+ days away, set status to PENDING_DELIVER
            newStatus = ManifestStatus.PENDING_DELIVER;
            logger.info("Manifest {} should be PENDING_DELIVER (delivery in {} days)", manifest.getTrackingNo(), diffDays);
        }
        
        // Only update if status has changed
        if (newStatus != originalStatus) {
            manifest.setStatus(newStatus);
            logger.info("Status change for manifest {}: {} -> {} (based on delivery date: {} days until delivery)", 
                      manifest.getTrackingNo(), originalStatus, newStatus, diffDays);
            
            // Save the manifest
            Manifest savedManifest = manifestRepository.save(manifest);
            
            // Log status change
            try {
                String currentUser = getCurrentUser();
                trackingLogService.logStatusChange(
                    manifest.getTrackingNo(),
                    originalStatus,
                    savedManifest.getStatus(),
                    currentUser,
                    "Status auto-updated based on delivery date"
                );
            } catch (Exception e) {
                logger.warn("Failed to log status change for {}: {}", manifest.getTrackingNo(), e.getMessage());
            }

            // Send WebSocket notification
            try {
                webSocketNotificationService.broadcastManifestStatusUpdate(
                    savedManifest,
                    originalStatus,
                    savedManifest.getStatus(),
                    "Status auto-updated based on delivery date"
                );
            } catch (Exception e) {
                logger.warn("Failed to send WebSocket notification for {}: {}", manifest.getTrackingNo(), e.getMessage());
            }
            
            return savedManifest;
        } else {
            logger.info("No status change required for manifest {} based on delivery date (current status: {}, days until delivery: {})",
                       manifest.getTrackingNo(), originalStatus, diffDays);
            return manifest;
        }
    }
    
    /**
     * Determines the appropriate status based on pallet pieces count
     * 
     * @param currentStatus Current manifest status
     * @param totalPiecesInPallets Total pieces in all pallets
     * @param manifestPieces Total pieces expected in manifest
     * @param noPallets Whether there are no pallets
     * @param manifest The manifest object to check delivery date
     * @return The appropriate status based on current conditions
     */
    private ManifestStatus determineStatusBasedOnPalletPieces(ManifestStatus currentStatus, int totalPiecesInPallets, int manifestPieces, boolean noPallets, Manifest manifest) {
        // Priority 1: DISCREPANCY - when pallet pieces exceed manifest pieces
        if (totalPiecesInPallets > manifestPieces) {
            return ManifestStatus.DISCREPANCY;
        }
        
        // Priority 2: INBOUNDED_TO_WAREHOUSE - when pieces match exactly and we have pallets
        if (totalPiecesInPallets == manifestPieces && !noPallets) {
            // Auto-transition to INBOUNDED_TO_WAREHOUSE from various statuses
            if (isEarlyWorkflowStage(currentStatus) || 
                currentStatus == ManifestStatus.INBOUNDING || 
                currentStatus == ManifestStatus.DISCREPANCY ||
                currentStatus == ManifestStatus.READY_TO_DELIVER ||
                currentStatus == ManifestStatus.PENDING_DELIVER) {
                return ManifestStatus.INBOUNDED_TO_WAREHOUSE;
            }
        }
        
        // Priority 3: INBOUNDING - when we have some pieces but not all
        if (totalPiecesInPallets > 0 && totalPiecesInPallets < manifestPieces) {
            // Auto-transition to INBOUNDING from various statuses
            if (isEarlyWorkflowStage(currentStatus) || 
                currentStatus == ManifestStatus.DISCREPANCY || 
                currentStatus == ManifestStatus.INBOUNDED_TO_WAREHOUSE ||
                currentStatus == ManifestStatus.READY_TO_DELIVER ||
                currentStatus == ManifestStatus.PENDING_DELIVER) {
                return ManifestStatus.INBOUNDING;
            }
        }
        
        // Priority 4: Revert to ARRIVED if no pallets and coming from auto-managed statuses
        if (noPallets || totalPiecesInPallets == 0) {
            if (currentStatus == ManifestStatus.DISCREPANCY || 
                currentStatus == ManifestStatus.INBOUNDING || 
                currentStatus == ManifestStatus.INBOUNDED_TO_WAREHOUSE ||
                currentStatus == ManifestStatus.READY_TO_DELIVER ||
                currentStatus == ManifestStatus.PENDING_DELIVER) {
                return ManifestStatus.ARRIVED;
            }
        }
        
        // No status change needed
        return currentStatus;
    }
    
    /**
     * Checks if the current status is in an early workflow stage where auto-transitions are allowed
     */
    private boolean isEarlyWorkflowStage(ManifestStatus status) {
        return status == ManifestStatus.CREATED || 
               status == ManifestStatus.ETA_TO_WAREHOUSE || 
               status == ManifestStatus.ARRIVED;
    }
    
    /**
     * Helper method to get the current authenticated user
     */
    private String getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getName() != null) {
                return authentication.getName();
            }
        } catch (Exception e) {
            logger.debug("Could not get current user from security context: {}", e.getMessage());
        }
        return "system";
    }

    /**
     * Scheduled task that runs daily at midnight to check and update manifest statuses
     * based on delivery date proximity. This ensures manifests transition from
     * PENDING_DELIVER to READY_TO_DELIVER automatically when delivery date approaches.
     * 
     * Alternative scheduling options:
     * - @Scheduled(cron = "0 0 6 * * *")  // Every 6 hours
     * - @Scheduled(cron = "0 0 8,20 * * *") // Twice daily at 8 AM and 8 PM
     * - @Scheduled(fixedRate = 3600000)     // Every hour (3600000 ms)
     */
    @Scheduled(cron = "0 0 0 * * *") // Runs at midnight every day
    @Transactional
    public void scheduledDeliveryDateStatusUpdate() {
        LocalDate today = LocalDate.now(java.time.ZoneId.of("Asia/Singapore"));
        logger.info("Starting scheduled delivery date status update task at {} (Singapore time)", today);

        try {
            // Find all manifests that are in delivery-related statuses and have delivery dates
            List<Manifest> manifestsToCheck = manifestRepository.findManifestsWithDeliveryDates(
                List.of(ManifestStatus.INBOUNDED_TO_WAREHOUSE, 
                       ManifestStatus.READY_TO_DELIVER, 
                       ManifestStatus.PENDING_DELIVER)
            );
            
            logger.info("Found {} manifests with delivery dates to check", manifestsToCheck.size());
            
            int updatedCount = 0;
            for (Manifest manifest : manifestsToCheck) {
                try {
                    Manifest originalManifest = new Manifest();
                    originalManifest.setTrackingNo(manifest.getTrackingNo());
                    originalManifest.setStatus(manifest.getStatus());
                    
                    // Check and update status based on delivery date
                    Manifest updatedManifest = checkAndUpdateStatusBasedOnDeliveryDate(manifest);
                    
                    // Count if status actually changed
                    if (!originalManifest.getStatus().equals(updatedManifest.getStatus())) {
                        updatedCount++;
                        logger.info("Scheduled update: Manifest {} status changed from {} to {} (delivery date: {})",
                                   updatedManifest.getTrackingNo(),
                                   originalManifest.getStatus(),
                                   updatedManifest.getStatus(),
                                   updatedManifest.getDeliveryDate());
                    }
                } catch (Exception e) {
                    logger.error("Error updating manifest {} during scheduled task: {}", 
                               manifest.getTrackingNo(), e.getMessage());
                    // Continue with other manifests even if one fails
                }
            }
            
            logger.info("Scheduled delivery date status update completed. Updated {} out of {} manifests", 
                       updatedCount, manifestsToCheck.size());
                       
        } catch (Exception e) {
            logger.error("Error during scheduled delivery date status update: {}", e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public int updateAllManifestStatusesBasedOnDeliveryDates() {
        logger.info("Manual trigger: Starting delivery date status update...");
        
        try {
            // Find all manifests that are in delivery-related statuses and have delivery dates
            List<Manifest> manifestsToCheck = manifestRepository.findManifestsWithDeliveryDates(
                List.of(ManifestStatus.INBOUNDED_TO_WAREHOUSE, 
                       ManifestStatus.READY_TO_DELIVER, 
                       ManifestStatus.PENDING_DELIVER)
            );
            
            logger.info("Manual trigger: Found {} manifests with delivery dates to check", manifestsToCheck.size());
            
            int updatedCount = 0;
            for (Manifest manifest : manifestsToCheck) {
                try {
                    ManifestStatus originalStatus = manifest.getStatus();
                    
                    // Check and update status based on delivery date
                    Manifest updatedManifest = checkAndUpdateStatusBasedOnDeliveryDate(manifest);
                    
                    // Count if status actually changed
                    if (!originalStatus.equals(updatedManifest.getStatus())) {
                        updatedCount++;
                        logger.debug("Manual trigger: Manifest {} status changed from {} to {}", 
                                   updatedManifest.getTrackingNo(), 
                                   originalStatus, 
                                   updatedManifest.getStatus());
                    }
                } catch (Exception e) {
                    logger.error("Manual trigger: Error updating manifest {} during manual update: {}", 
                               manifest.getTrackingNo(), e.getMessage());
                    // Continue with other manifests even if one fails
                }
            }
            
            logger.info("Manual trigger: Delivery date status update completed. Updated {} out of {} manifests", 
                       updatedCount, manifestsToCheck.size());
            
            return updatedCount;
                        
        } catch (Exception e) {
            logger.error("Manual trigger: Error during delivery date status update: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * Event listener for delivery date changes
     * Triggers immediate status check when delivery dates are modified
     */
    @EventListener
    @Async
    @Transactional
    public void handleDeliveryDateChange(DeliveryDateChangedEvent event) {
        try {
            Manifest manifest = event.getManifest();
            logger.info("Processing delivery date change event for manifest {}: {} -> {}",
                       manifest.getTrackingNo(),
                       event.getOldDeliveryDate(),
                       event.getNewDeliveryDate());

            // Only process if manifest has a delivery date and is in relevant status
            if (manifest.getDeliveryDate() != null &&
                (manifest.getStatus() == ManifestStatus.INBOUNDED_TO_WAREHOUSE ||
                 manifest.getStatus() == ManifestStatus.PENDING_DELIVER ||
                 manifest.getStatus() == ManifestStatus.READY_TO_DELIVER)) {

                ManifestStatus originalStatus = manifest.getStatus();
                Manifest updatedManifest = checkAndUpdateStatusBasedOnDeliveryDate(manifest);

                if (!originalStatus.equals(updatedManifest.getStatus())) {
                    logger.info("Event-driven status update: Manifest {} changed from {} to {} due to delivery date change",
                               updatedManifest.getTrackingNo(), originalStatus, updatedManifest.getStatus());

                    // Send WebSocket notification for event-driven update
                    try {
                        webSocketNotificationService.broadcastManifestStatusUpdate(
                            updatedManifest,
                            originalStatus,
                            updatedManifest.getStatus(),
                            "Status updated due to delivery date change by " + event.getChangedBy()
                        );
                    } catch (Exception e) {
                        logger.warn("Failed to send WebSocket notification for event-driven update {}: {}",
                                   updatedManifest.getTrackingNo(), e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error processing delivery date change event for manifest {}: {}",
                        event.getManifest().getTrackingNo(), e.getMessage(), e);
        }
    }
}