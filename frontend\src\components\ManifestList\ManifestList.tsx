import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Alert,
  Snackbar,
  Button,
  Card,
  CardHeader,
  CardContent,
  Divider,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  DialogContentText,
  Paper,
  Fab,
  Zoom,
  InputAdornment,
  IconButton
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import InfoIcon from '@mui/icons-material/Info';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import { Manifest, ManifestStatus } from '../../types/manifest';
import { ManifestListProps } from './types';
import { LocationZone } from '../../types/location';
import { VehicleType } from '../../types/Vehicle';
import {
  ManifestListHeader,
  ManifestListToolbar,
  ManifestListFilters,
  ManifestListColumns,
  ManifestListGrid
} from './components';
import {
  useManifestSelection,
  useManifestFilters,
  useManifestDialogs
} from './hooks';
// Import the hook directly from the file to avoid the "Property doesn't exist on type 'void'" error
import { useManifestColumns } from './hooks/useManifestColumns';
import { formatTimeSlot, getTimeSlotIdFromDate, formatDateForExport, formatDateOnly, formatDateOnlyLocal, parseDateString } from '../../utils/dateUtils';

// Import actual dialog components
// import ManifestLabelDialog from '../ManifestLabelDialog';
import PalletManagementDialog from '../PalletManagementDialog';
import DeliveryDateDialog from '../DeliveryDateDialog';
import QuickDeliveryVehicleDialog from '../QuickDeliveryVehicleDialog';
import { formatDate, formatDateString, formatTimestamp } from '../../utils/dateUtils';
import Excel from 'exceljs';
import { saveAs } from 'file-saver';

import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ViewColumnIcon from '@mui/icons-material/ViewColumn';

// Import services for API calls
import manifestService from '../../services/manifest.service';
import locationService from '../../services/location.service';
import * as vehicleService from '../../services/vehicle.service';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../contexts/ToastContext';
import { useWebSocket } from '../../hooks/useWebSocket';

// Mock dialog components with the expected props
// These can be replaced with the real components once they're available
const ManifestLabelDialog: React.FC<{
  open: boolean;
  manifest: Manifest;
  onClose: () => void;
  onGenerate: (count: number) => void;
}> = ({ open, manifest, onClose, onGenerate }) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Print Manifest Label</DialogTitle>
      <DialogContent>
        <DialogContentText>
          Generate shipping label for manifest {manifest.trackingNo}
        </DialogContentText>
        <TextField
          autoFocus
          margin="dense"
          label="Number of copies"
          type="number"
          fullWidth
          defaultValue={1}
          inputProps={{ min: 1, max: 10 }}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={() => onGenerate(1)} color="primary">Generate</Button>
      </DialogActions>
    </Dialog>
  );
};





// Default empty arrays for the resources
const EMPTY_LOCATION_ZONES: LocationZone[] = [];
const EMPTY_VEHICLE_TYPES: VehicleType[] = [];

/**
 * A reusable component for displaying and managing manifest lists.
 * It can be used in both the main manifest management page and the container detail page.
 */
const ManifestList: React.FC<ManifestListProps> = ({
  // Data props
  manifests: propManifests,
  loading: propLoading,
  error: propError,
  
  // Context props
  containerContext,
  isContainerView = false,
  
  // Resource props
  locationZones: propLocationZones = EMPTY_LOCATION_ZONES,
  vehicleTypes: propVehicleTypes = EMPTY_VEHICLE_TYPES,
  
  // Loading states
  locationsLoading: propLocationsLoading = false,
  vehicleTypesLoading: propVehicleTypesLoading = false,
  
  // Callback props
  onManifestUpdate,
  onManifestDelete,
  onBulkDelete,
  onBulkLabelGeneration,
  onExportToExcel,
  onStatusChange,
  onRefresh,

  // Action button callbacks
  onCreateManifest,
  onUploadManifest,

  // UI customization
  customActions,

  // Viewport options
  viewportOptions
}) => {
  // Auth context for API calls
  const { currentUser } = useAuth();
  const toast = useToast();

  // WebSocket for real-time updates
  const { manifestStatusUpdates } = useWebSocket({
    containerNo: containerContext?.containerNo
  });
  
  // State for data fetched from API
  const [manifests, setManifests] = useState<Manifest[]>(propManifests || []);
  const [loading, setLoading] = useState<boolean>(propLoading || true);
  const [error, setError] = useState<string | null>(propError || null);
  const [locationZones, setLocationZones] = useState<LocationZone[]>(propLocationZones);
  const [vehicleTypes, setVehicleTypes] = useState<VehicleType[]>(propVehicleTypes);
  const [locationsLoading, setLocationsLoading] = useState<boolean>(propLocationsLoading);
  const [vehicleTypesLoading, setVehicleTypesLoading] = useState<boolean>(propVehicleTypesLoading);
  
  // State to always show scroll to top button
  const [showScrollToTop] = useState(true); // Always show the button

  // Ref for the manifest list component container
  const manifestListRef = useRef<HTMLDivElement>(null);

  // Ref for the "Manifest List" title
  const manifestTitleRef = useRef<HTMLDivElement>(null);

  // Function to handle scroll to above the "Manifest List" title with smooth animation
  const handleScrollToTop = useCallback(() => {
    if (manifestTitleRef.current) {
      // Get the position of the "Manifest List" title
      const rect = manifestTitleRef.current.getBoundingClientRect();
      const scrollTop = window.scrollY || document.documentElement.scrollTop;
      // Add significant offset to scroll well above the title (e.g., 100px above)
      const targetY = rect.top + scrollTop - 100;

      // Scroll to the "Manifest List" title (vertical only)
      if ('scrollBehavior' in document.documentElement.style) {
        window.scrollTo({
          top: targetY,
          behavior: 'smooth'
        });
      } else {
        // Fallback for older browsers with smooth animation
        const startY = window.scrollY;
        const distance = targetY - startY;
        const duration = 500; // 500ms animation
        let startTime: number | null = null;

        const animateScroll = (currentTime: number) => {
          if (startTime === null) startTime = currentTime;
          const timeElapsed = currentTime - startTime;
          const progress = Math.min(timeElapsed / duration, 1);

          // Easing function for smooth animation
          const ease = progress * (2 - progress);
          window.scrollTo(0, startY + (distance * ease));

          if (progress < 1) {
            requestAnimationFrame(animateScroll);
          }
        };

        requestAnimationFrame(animateScroll);
      }
    } else if (manifestListRef.current) {
      // Fallback to manifest list container if title ref is not available
      const rect = manifestListRef.current.getBoundingClientRect();
      const scrollTop = window.scrollY || document.documentElement.scrollTop;
      const targetY = rect.top + scrollTop - 100;

      if ('scrollBehavior' in document.documentElement.style) {
        window.scrollTo({
          top: targetY,
          behavior: 'smooth'
        });
      } else {
        window.scrollTo(0, targetY);
      }
    } else {
      // Final fallback to page top if no refs are available
      if ('scrollBehavior' in document.documentElement.style) {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } else {
        window.scrollTo(0, 0);
      }
    }
  }, []);

  // Selection state management
  const { 
    selectedManifests, 
    handleSelectionChange, 
    clearSelection 
  } = useManifestSelection();
  
  // Filter state management
  const {
    filters,
    filteredManifests,
    handleFilterChange,
    resetFilters
  } = useManifestFilters(manifests);

  // Calculate totals for filtered manifests
  const calculateTotals = (manifests: Manifest[]) => {
    const result = manifests.reduce((totals, manifest) => {
      return {
        totalCBM: totals.totalCBM + (manifest.cbm || 0),
        totalPallets: totals.totalPallets + (manifest.actualPalletsCount || 0),
        totalPieces: totals.totalPieces + (manifest.pieces || 0),
        totalInboundPieces: totals.totalInboundPieces + (manifest.inboundPieces || 0),
      };
    }, {
      totalCBM: 0,
      totalPallets: 0,
      totalPieces: 0,
      totalInboundPieces: 0,
    });

    return result;
  };

  // Calculate status counts from original manifests (not filtered)
  const calculateStatusCounts = (manifests: Manifest[]) => {
    return manifests.reduce((acc, manifest) => {
      const status = manifest.status;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<ManifestStatus, number>);
  };

  // Calculate totals for the filtered manifests and status counts from all manifests
  const totals = useMemo(() => {
    const filteredTotals = calculateTotals(filteredManifests);
    const statusCounts = calculateStatusCounts(manifests); // Use original manifests for status counts

    return {
      ...filteredTotals,
      byStatus: statusCounts
    };
  }, [filteredManifests, manifests]);
  
  // Dialog management
  const {
    dialogs,
    dialogProps,
    loading: dialogLoading,
    error: dialogError,
    openDialog,
    closeDialog,
    actions
  } = useManifestDialogs({
    onDelete: async (trackingNo) => {
      try {
        if (!currentUser?.token) {
          throw new Error('Authentication token not available');
        }
        
        if (onManifestDelete) {
          // Use the provided callback if available
          await onManifestDelete(trackingNo);
        } else {
          // Otherwise use the service directly
          await manifestService.deleteManifest(trackingNo, currentUser?.token || '');
          // Update local state after successful delete
          setManifests(prevManifests => 
            prevManifests.filter(m => m.trackingNo !== trackingNo)
          );
          toast.success(`Manifest ${trackingNo} deleted successfully`);
        }
        // Clear selection after successful deletion to avoid showing deleted manifest as selected
        clearSelection();
      } catch (err: any) {
        console.error('Error deleting manifest:', err);
        toast.error(`Failed to delete manifest: ${err.message}`);
      }
    },
    onBulkDelete: async (trackingNos) => {
      try {
        if (!currentUser?.token) {
          throw new Error('Authentication token not available');
        }
        
        if (onBulkDelete) {
          // Use the provided callback if available
          await onBulkDelete(trackingNos);
        } else {
          // Otherwise delete one by one
          const promises = trackingNos.map(trackingNo => 
            manifestService.deleteManifest(trackingNo, currentUser?.token || '')
          );
          await Promise.all(promises);
          
          // Update local state after successful delete
          setManifests(prevManifests => 
            prevManifests.filter(m => !trackingNos.includes(m.trackingNo || ''))
          );
          toast.success(`${trackingNos.length} manifests deleted successfully`);
        }
        // Clear selection after successful bulk deletion to avoid showing deleted manifests as selected
        clearSelection();
      } catch (err: any) {
        console.error('Error bulk deleting manifests:', err);
        toast.error(`Failed to delete manifests: ${err.message}`);
      }
    },
    onStatusChange: async (trackingNo, status) => {
      try {
        if (!currentUser?.token) {
          throw new Error('Authentication token not available');
        }
        
        if (onStatusChange) {
          // Use the provided callback if available
          await onStatusChange(trackingNo, status);
        } else {
          // Otherwise use the service directly
          await manifestService.updateManifestStatus(trackingNo, status, currentUser?.token || '');
          
          // Update local state after successful status change
          setManifests(prevManifests => 
            prevManifests.map(m => 
              m.trackingNo === trackingNo ? { ...m, status } : m
            )
          );
          toast.success(`Manifest ${trackingNo} status updated to ${status}`);
        }
      } catch (err: any) {
        console.error('Error updating manifest status:', err);
        toast.error(`Failed to update status: ${err.message}`);
      }
    },
    onUpdate: async (manifest) => {
      try {
        if (!currentUser?.token || !manifest.trackingNo) {
          throw new Error('Authentication token or tracking number not available');
        }
        
      if (onManifestUpdate) {
          // Use the provided callback if available
          await onManifestUpdate(manifest);
        } else {
          // Otherwise use the service directly
          const response = await manifestService.updateManifest(
            manifest.trackingNo, 
            manifest, 
            currentUser?.token || ''
          );
          
          // Update local state after successful update
          setManifests(prevManifests => 
            prevManifests.map(m => 
              m.trackingNo === manifest.trackingNo ? response.data : m
            )
          );
          toast.success(`Manifest ${manifest.trackingNo} updated successfully`);
        }
      } catch (err: any) {
        console.error('Error updating manifest:', err);
        toast.error(`Failed to update manifest: ${err.message}`);
      }
    },
    onDeliveryDateChange: async (trackingNo, date, timeSlot = null) => {
      try {
        if (!currentUser?.token) {
          throw new Error('Authentication token not available');
        }

        const manifest = manifests.find(m => m.trackingNo === trackingNo);
        if (!manifest) {
          throw new Error('Manifest not found');
        }

        const updatedManifest = {
            ...manifest,
            deliveryDate: date ? formatDateOnlyLocal(date) : null,
            // Use the provided time slot or default to 'no_time' (same as ManifestManagement)
            timeSlot: date ? (timeSlot || 'no_time') : null
        };
        
        if (onManifestUpdate) {
          // Use the provided callback if available
          await onManifestUpdate(updatedManifest);
        } else {
          // Otherwise use the service directly
          const response = await manifestService.updateManifest(
            trackingNo, 
            updatedManifest, 
            currentUser.token
          );
          
          // Update local state after successful update
          setManifests(prevManifests => 
            prevManifests.map(m => 
              m.trackingNo === trackingNo ? response.data : m
            )
          );
          toast.success(date ? `Delivery date for ${trackingNo} updated successfully` : `Delivery date for ${trackingNo} removed successfully`);
        }
      } catch (err: any) {
        console.error('Error updating delivery date:', err);
        toast.error(`Failed to update delivery date: ${err.message}`);
      }
    },
    onDeliveryVehicleChange: async (trackingNo, vehicle) => {
      try {
        if (!currentUser?.token) {
          throw new Error('Authentication token not available');
        }
        
      if (onManifestUpdate) {
        const manifest = manifests.find(m => m.trackingNo === trackingNo);
        if (manifest) {
            await onManifestUpdate({
            ...manifest,
            deliveryVehicle: vehicle
          });
        }
        } else {
          // Use the dedicated API endpoint
          await manifestService.updateManifestDeliveryVehicle(
            trackingNo, 
            vehicle, 
            currentUser.token
          );
          
          // Update local state after successful update
          setManifests(prevManifests => 
            prevManifests.map(m => 
              m.trackingNo === trackingNo ? { ...m, deliveryVehicle: vehicle } : m
            )
          );
          toast.success(`Delivery vehicle for ${trackingNo} updated successfully`);
        }
      } catch (err: any) {
        console.error('Error updating delivery vehicle:', err);
        toast.error(`Failed to update delivery vehicle: ${err.message}`);
      }
    },
    onPrintLabel: async (manifest, count) => {
      if (onBulkLabelGeneration) {
        // Create an array with the same manifest repeated 'count' times
        const labelsToGenerate = Array(count).fill(manifest);
        onBulkLabelGeneration(labelsToGenerate);
      }
    }
  });
  
  // Effect to fetch data if not provided via props
  useEffect(() => {
    const fetchData = async () => {
      if (!currentUser?.token) return;
      
      // If manifests are provided via props, use those
      if (propManifests) {
        setManifests(propManifests);
        setLoading(propLoading || false);
        setError(propError || null);
        return;
      }
      
      try {
        setLoading(true);
        setError(null);
        
        // Fetch manifests based on context
        let manifestsResponse;
        if (containerContext && containerContext.containerNo) {
          // If in container context, fetch manifests for that container
          manifestsResponse = await manifestService.getManifestsByContainer(
            containerContext.containerNo, 
            currentUser?.token || ''
          );
        } else {
          // Otherwise fetch all manifests
          manifestsResponse = await manifestService.getAllManifests(currentUser?.token || '');
        }
        
        if (manifestsResponse && manifestsResponse.success && manifestsResponse.data) {
          console.log('Manifests API response data:', manifestsResponse.data);
          // Check container data
          if (manifestsResponse.data.length > 0) {
            console.log('First manifest container:', manifestsResponse.data[0].container);
            console.log('First manifest createdDate:', manifestsResponse.data[0].createdDate);
          }
          
          // Sort manifests by internalId before setting them
          const sortedManifests = [...manifestsResponse.data].sort((a, b) => {
            const aId = a.internalId || '';
            const bId = b.internalId || '';
            
            // Extract letter prefix and numbers
            const aPrefix = aId.match(/^[A-Za-z]+/)?.[0] || '';
            const bPrefix = bId.match(/^[A-Za-z]+/)?.[0] || '';
            
            // If prefixes are different, sort by prefix
            if (aPrefix !== bPrefix) {
              return aPrefix.localeCompare(bPrefix);
            }
            
            // If prefixes are the same, extract and compare the numbers
            const aNum = parseInt(aId.replace(/^[A-Za-z]+\-?/, '')) || 0;
            const bNum = parseInt(bId.replace(/^[A-Za-z]+\-?/, '')) || 0;
            
            return aNum - bNum;
          });
          
          setManifests(sortedManifests);
        } else {
          throw new Error(manifestsResponse?.message || 'Failed to fetch manifests');
        }
      } catch (err: any) {
        console.error('Error fetching manifests:', err);
        setError(`Failed to load manifests: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [currentUser?.token, containerContext, propManifests, propLoading, propError]);
  
  // Effect to fetch location zones if not provided via props
  useEffect(() => {
    const fetchLocationZones = async () => {
      if (!currentUser?.token || propLocationZones.length > 0) return;
      
      try {
        setLocationsLoading(true);
        const response = await locationService.getAllLocationZones(currentUser?.token || '');
        if (response && response.success && response.data) {
          setLocationZones(response.data);
        }
      } catch (err) {
        console.error('Error fetching location zones:', err);
      } finally {
        setLocationsLoading(false);
      }
    };
    
    fetchLocationZones();
  }, [currentUser?.token, propLocationZones]);
  
  // Effect to fetch vehicle types if not provided via props
  useEffect(() => {
    const fetchVehicleTypes = async () => {
      if (!currentUser?.token || propVehicleTypes.length > 0) return;
      
      try {
        setVehicleTypesLoading(true);
        // The getVehicleTypes function doesn't take a token parameter and returns VehicleType[] directly
        const vehicleTypes = await vehicleService.getVehicleTypes();
        setVehicleTypes(vehicleTypes);
      } catch (err) {
        console.error('Error fetching vehicle types:', err);
      } finally {
        setVehicleTypesLoading(false);
      }
    };
    
    fetchVehicleTypes();
  }, [currentUser?.token, propVehicleTypes]);

  // Add event listener for selective pallet updates
  useEffect(() => {
    const handleSelectivePalletUpdate = (event: CustomEvent) => {
      const { type, manifestTrackingNo, palletCount, inboundedPieces } = event.detail;

      if (type === 'pallet_created') {
        console.log(`🎯 ManifestList selective update: Pallet created for manifest ${manifestTrackingNo}, new count: ${palletCount}, inbounded pieces: ${inboundedPieces}`);

        // Update only the specific manifest's pallet count and inbounded pieces without full refresh
        setManifests(prevManifests => {
          const oldManifest = prevManifests.find(m => m.trackingNo === manifestTrackingNo);
          console.log(`📊 BEFORE update - manifest ${manifestTrackingNo}:`);
          console.log(`   Pallet count: ${oldManifest?.actualPalletsCount || 0}`);
          console.log(`   Inbound pieces: ${oldManifest?.inboundPieces || 0}`);

          const updatedManifests = prevManifests.map(manifest =>
            manifest.trackingNo === manifestTrackingNo
              ? {
                  ...manifest,
                  actualPalletsCount: palletCount,
                  inboundPieces: inboundedPieces
                }
              : manifest
          );

          const newManifest = updatedManifests.find(m => m.trackingNo === manifestTrackingNo);
          console.log(`📊 AFTER update - manifest ${manifestTrackingNo}:`);
          console.log(`   Pallet count: ${newManifest?.actualPalletsCount || 0}`);
          console.log(`   Inbound pieces: ${newManifest?.inboundPieces || 0}`);

          return updatedManifests;
        });

        // Mark that we've handled the selective update
        const marker = document.createElement('div');
        marker.setAttribute('data-selective-update-handled', 'true');
        marker.style.display = 'none';
        document.body.appendChild(marker);

        console.log('✅ ManifestList: Manifest pallet count and inbound pieces updated selectively without page refresh');
      }
    };

    // Add event listener
    window.addEventListener('pallet-operation-selective-update', handleSelectivePalletUpdate as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('pallet-operation-selective-update', handleSelectivePalletUpdate as EventListener);
    };
  }, []);

  // Add event listener for bulk manifest creation updates
  useEffect(() => {
    const handleBulkManifestCreation = (event: CustomEvent) => {
      const { type, manifests, count } = event.detail;

      if (type === 'bulk_created' && manifests && Array.isArray(manifests)) {
        console.log(`🎯 ManifestList: Received bulk manifest creation event - ${count} new manifests`);

        // Add the new manifests to the existing list without full refresh
        setManifests(prevManifests => {
          // Check for duplicates to avoid adding the same manifest twice
          const existingTrackingNos = new Set(prevManifests.map(m => m.trackingNo));
          const newManifests = manifests.filter((manifest: any) => !existingTrackingNos.has(manifest.trackingNo));

          if (newManifests.length > 0) {
            console.log(`📊 Adding ${newManifests.length} new manifests to the list:`);
            newManifests.forEach((manifest: any) => {
              console.log(`   + ${manifest.trackingNo} (${manifest.customerName})`);
            });

            // Combine existing and new manifests, then sort by creation date (newest first)
            const updatedManifests = [...prevManifests, ...newManifests];
            const sortedManifests = updatedManifests.sort((a, b) => {
              const dateA = new Date(a.createdDate || 0).getTime();
              const dateB = new Date(b.createdDate || 0).getTime();
              return dateB - dateA; // Newest first
            });

            console.log(`✅ ManifestList: Added ${newManifests.length} new manifests without page refresh`);
            return sortedManifests;
          } else {
            console.log('ℹ️ No new manifests to add (all already exist in the list)');
            return prevManifests;
          }
        });
      }
    };

    // Add event listener
    window.addEventListener('manifests-bulk-created', handleBulkManifestCreation as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('manifests-bulk-created', handleBulkManifestCreation as EventListener);
    };
  }, []);

  // Add event listener for manifest deletion updates
  useEffect(() => {
    const handleManifestDeletion = (event: CustomEvent) => {
      const { type, trackingNo } = event.detail;

      if (type === 'deleted' && trackingNo) {
        console.log(`🗑️ ManifestList: Received manifest deletion event for ${trackingNo}`);

        // Remove the deleted manifest from the list
        setManifests(prevManifests => {
          const filteredManifests = prevManifests.filter(m => m.trackingNo !== trackingNo);
          console.log(`📊 ManifestList: Removed manifest ${trackingNo}, ${prevManifests.length} -> ${filteredManifests.length} manifests`);
          return filteredManifests;
        });

        // Clear selection if the deleted manifest was selected
        clearSelection();

        console.log('✅ ManifestList: Manifest removed from list without page refresh');
      }
    };

    const handleBulkManifestDeletion = (event: CustomEvent) => {
      const { type, trackingNos, successful, failed } = event.detail;

      if (type === 'bulk_deleted' && trackingNos && Array.isArray(trackingNos)) {
        console.log(`🗑️ ManifestList: Received bulk manifest deletion event for ${trackingNos.length} manifests (${successful} successful, ${failed} failed)`);

        // Remove the deleted manifests from the list
        setManifests(prevManifests => {
          const filteredManifests = prevManifests.filter(m => !trackingNos.includes(m.trackingNo || ''));
          console.log(`📊 ManifestList: Removed ${trackingNos.length} manifests, ${prevManifests.length} -> ${filteredManifests.length} manifests`);
          return filteredManifests;
        });

        // Clear selection since deleted manifests were likely selected
        clearSelection();

        console.log('✅ ManifestList: Manifests removed from list without page refresh');
      }
    };

    // Add event listeners
    window.addEventListener('manifest-deleted', handleManifestDeletion as EventListener);
    window.addEventListener('manifests-bulk-deleted', handleBulkManifestDeletion as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('manifest-deleted', handleManifestDeletion as EventListener);
      window.removeEventListener('manifests-bulk-deleted', handleBulkManifestDeletion as EventListener);
    };
  }, []);

  // Handle WebSocket manifest status updates
  useEffect(() => {
    if (manifestStatusUpdates.length > 0) {
      const latestUpdate = manifestStatusUpdates[0];

      // Update the manifest in the local state
      setManifests(prevManifests =>
        prevManifests.map(manifest =>
          manifest.trackingNo === latestUpdate.manifest.trackingNo
            ? {
                ...manifest,
                status: latestUpdate.newStatus as ManifestStatus,
                pieces: latestUpdate.manifest.pieces,
                actualPalletsCount: latestUpdate.manifest.actualPalletsCount,
                deliveryDate: latestUpdate.manifest.deliveryDate ? new Date(latestUpdate.manifest.deliveryDate) : null
              }
            : manifest
        )
      );

      // Show toast notification for real-time updates
      toast.info(
        `Manifest ${latestUpdate.manifest.trackingNo} status updated to ${latestUpdate.newStatus}`,
        {
          autoClose: 3000,
          position: 'bottom-right'
        }
      );

      console.log('🔄 ManifestList: Applied real-time status update via WebSocket:', latestUpdate);
    }
  }, [manifestStatusUpdates, toast]);

  // Quick edit handlers for remarks fields
  const handleQuickEditRemarks = async (manifest: Manifest, field: 'remarks' | 'driverRemarks', newValue: string) => {
    try {
      if (!manifest || !manifest.trackingNo) {
        throw new Error('Invalid manifest object provided');
      }

      // Create a clean copy of the manifest with the updated field
      // Use JSON parse/stringify to remove any circular references or non-serializable data
      const cleanManifest = JSON.parse(JSON.stringify(manifest));
      const updateData: Manifest = {
        ...cleanManifest,
        [field]: newValue
      };

      if (onManifestUpdate) {
        // Use provided callback - pass the full manifest with updated field
        const updatedManifest = { ...manifest, [field]: newValue };
        await onManifestUpdate(updatedManifest);
      } else if (currentUser?.token) {
        // Local implementation for standalone usage - use dedicated remarks update method
        const manifestService = (await import('../../services/manifest.service')).default;
        const response = await manifestService.updateManifestRemarks(
          manifest.trackingNo,
          field,
          newValue,
          currentUser.token
        );

        if (response.success) {
          toast.success(`${field === 'driverRemarks' ? 'Driver remarks' : 'CS remarks'} updated successfully`);
          // Update local state
          setManifests(prev =>
            prev.map(m =>
              m.trackingNo === manifest.trackingNo
                ? { ...m, [field]: newValue }
                : m
            )
          );
        } else {
          throw new Error(response.message || 'Failed to update');
        }
      } else {
        throw new Error('No authentication token available');
      }
    } catch (error) {
      console.error(`Failed to update ${field}:`, error);
      toast.error(`Failed to update ${field === 'driverRemarks' ? 'driver remarks' : 'CS remarks'}`);
      throw error; // Re-throw to let the QuickEditCell handle the error
    }
  };

  // Action handlers for columns - memoized to prevent infinite re-renders
  const columnActionHandlers = useMemo(() => ({
    onEdit: (manifest: Manifest) => openDialog('editManifest', { manifest }),
    onDelete: (trackingNo: string) => openDialog('confirmDelete', {
      trackingNo,
      title: 'Confirm Delete',
      message: `Are you sure you want to delete manifest ${trackingNo}?`
    }),
    onEditDeliveryDate: (trackingNo: string) => {
      const manifest = manifestsRef.current.find(m => m.trackingNo === trackingNo);
      const currentDate = manifest?.deliveryDate ? parseDateString(manifest.deliveryDate) : null;
      const currentTimeSlot = manifest?.timeSlot || (currentDate ? getTimeSlotIdFromDate(currentDate) : null);
      openDialog('deliveryDate', { trackingNo, internalId: manifest?.internalId, currentDate, currentTimeSlot });
    },
    onEditDeliveryVehicle: (manifest: Manifest) => openDialog('deliveryVehicle', {
      manifest,
      currentVehicle: manifest.deliveryVehicle
    }),
    onQuickEditRemarks: handleQuickEditRemarks,
    onManagePallets: (manifest: Manifest) => openDialog('palletManagement', { manifest }),
  }), [openDialog, onManifestUpdate, handleQuickEditRemarks]); // Removed manifests dependency to prevent column width reset
  
  // Column visibility management
  const { 
    visibleColumns, 
    columns, 
    handleColumnToggle, 
    resetColumnVisibility,
    selectAllColumns,
    selectNoColumns
  } = useManifestColumns(isContainerView, columnActionHandlers, viewportOptions);
  
  // Handler for refreshing data
  const handleRefresh = async () => {
    if (onRefresh) {
      onRefresh();
      clearSelection();
    } else {
      // If no onRefresh callback is provided, refresh data directly
      try {
        if (!currentUser?.token) {
          throw new Error('Authentication token not available');
        }
        
        setLoading(true);
        setError(null);
        
        // Fetch manifests based on context
        let manifestsResponse;
        if (containerContext && containerContext.containerNo) {
          // If in container context, fetch manifests for that container
          manifestsResponse = await manifestService.getManifestsByContainer(
            containerContext.containerNo, 
            currentUser?.token || ''
          );
        } else {
          // Otherwise fetch all manifests
          manifestsResponse = await manifestService.getAllManifests(currentUser?.token || '');
        }
        
        if (manifestsResponse && manifestsResponse.success && manifestsResponse.data) {
          console.log('Manifests API response data:', manifestsResponse.data);
          // Check container data
          if (manifestsResponse.data.length > 0) {
            console.log('First manifest container:', manifestsResponse.data[0].container);
            console.log('First manifest createdDate:', manifestsResponse.data[0].createdDate);
          }
          
          // Sort manifests by internalId before setting them
          const sortedManifests = [...manifestsResponse.data].sort((a, b) => {
            const aId = a.internalId || '';
            const bId = b.internalId || '';
            
            // Extract letter prefix and numbers
            const aPrefix = aId.match(/^[A-Za-z]+/)?.[0] || '';
            const bPrefix = bId.match(/^[A-Za-z]+/)?.[0] || '';
            
            // If prefixes are different, sort by prefix
            if (aPrefix !== bPrefix) {
              return aPrefix.localeCompare(bPrefix);
            }
            
            // If prefixes are the same, extract and compare the numbers
            const aNum = parseInt(aId.replace(/^[A-Za-z]+\-?/, '')) || 0;
            const bNum = parseInt(bId.replace(/^[A-Za-z]+\-?/, '')) || 0;
            
            return aNum - bNum;
          });
          
          setManifests(sortedManifests);
          clearSelection();
          toast.success('Manifests refreshed successfully');
        } else {
          throw new Error(manifestsResponse?.message || 'Failed to refresh manifests');
        }
      } catch (err: any) {
        console.error('Error refreshing manifests:', err);
        setError(`Failed to refresh manifests: ${err.message}`);
        toast.error(`Failed to refresh: ${err.message}`);
      } finally {
        setLoading(false);
      }
    }
  };
  
  // Handler for resetting column visibility
  const handleResetColumnVisibility = () => {
    // Call the reset function from the hook
    resetColumnVisibility();
  };
  
  // Handler for selecting all columns
  const handleSelectAllColumns = () => {
    // Call the select all function from the hook
    selectAllColumns();
  };
  
  // Handler for selecting no columns
  const handleSelectNoColumns = () => {
    // Call the select none function from the hook
    selectNoColumns();
  };
  
  // Handler for column visibility changes
  const handleColumnVisibilityChange = (newVisibleColumns: Record<string, boolean>) => {
    // Update column visibility in the useManifestColumns hook
    Object.keys(newVisibleColumns).forEach(column => {
      if (newVisibleColumns[column] !== visibleColumns[column]) {
        handleColumnToggle(column);
      }
    });
  };
  
  // Handler for bulk operations
  const handleBulkLabelGeneration = () => {
    if (onBulkLabelGeneration && selectedManifests.length > 0) {
      onBulkLabelGeneration(selectedManifests);
    }
  };
  
  // Handler for exporting data
  const handleExportToExcel = () => {
    if (onExportToExcel) {
      // Export either selected manifests or all filtered manifests
      const manifestsToExport = selectedManifests.length > 0
        ? selectedManifests
        : filteredManifests;

      onExportToExcel(manifestsToExport);
    } else {
      // Implement local export if no callback is provided
      exportToExcel(selectedManifests.length > 0 ? selectedManifests : filteredManifests);
    }
  };

  // Handler for exporting data for RTrack
  const handleExportForRTrack = () => {
    const manifestsToExport = selectedManifests.length > 0
      ? selectedManifests
      : filteredManifests;

    exportForRTrack(manifestsToExport);
  };
  
  // Local implementation of export to Excel functionality
  const exportToExcel = async (manifestsToExport: Manifest[]) => {
    try {
      if (manifestsToExport.length === 0) {
        toast.warning('No manifests to export');
        return;
      }
      
      // Create a new workbook
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('Manifests Export');
      
      // Get visible columns
      const visibleColumnDefs = columns
        .filter(col => visibleColumns[col.field])
        .filter(col => col.field !== 'actions') // Exclude 'actions' column
        .sort((a, b) => {
          // Ensure columns are in their displayed order
          const aIndex = columns.findIndex(c => c.field === a.field);
          const bIndex = columns.findIndex(c => c.field === b.field);
          return aIndex - bIndex;
        });
        
      // Define Excel columns based on visible grid columns
      const excelColumns = visibleColumnDefs.map(col => ({
        header: col.headerName?.replace('*', '') || col.field.charAt(0).toUpperCase() + col.field.slice(1).replace(/([A-Z])/g, ' $1').trim(),
        key: col.field,
        width: Math.max(15, (col.headerName?.length || 10) * 1.2)
      }));
      
      // Add columns to worksheet
      worksheet.columns = excelColumns;
      
      // Style the header row
      worksheet.getRow(1).eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '4F81BD' } // Blue background
        };
        cell.font = {
          bold: true,
          color: { argb: 'FFFFFF' } // White text
        };
        cell.alignment = {
          vertical: 'middle',
          horizontal: 'center'
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
      
      // Process manifest data for Excel
      const processedData = manifestsToExport.map(manifest => {
        const rowData: Record<string, any> = {};
        
        // Process each column
        visibleColumnDefs.forEach(col => {
          const field = col.field;
          
          // Handle special fields with nested objects
          if (field === 'client') {
            rowData[field] = manifest.client?.username || '';
          } 
          else if (field === 'container') {
            rowData[field] = manifest.container?.containerNo || '';
          }
          else if (field === 'driver') {
            rowData[field] = manifest.driver?.username || 'Not Assigned';
          }
          // Handle date fields
          else if (field === 'createdDate' || field === 'deliveryDate' || field === 'deliveredDate') {
            const dateValue = manifest[field as keyof Manifest];
            rowData[field] = dateValue ? formatDateForExport(dateValue as string) : '';
          }
          // Handle timeSlot field - convert ID to human-readable label
          else if (field === 'timeSlot') {
            const timeSlotId = manifest.timeSlot || (manifest.deliveryDate ? getTimeSlotIdFromDate(manifest.deliveryDate) : null);
            rowData[field] = formatTimeSlot(timeSlotId);
          }
          // Handle status field
          else if (field === 'status') {
            rowData[field] = manifest.status?.replace(/_/g, ' ') || '';
          }
          // Handle regular fields
          else {
            const value = manifest[field as keyof Manifest];
            rowData[field] = value !== undefined ? value : '';
          }
        });
        
        return rowData;
      });
      
      // Add rows to worksheet
      processedData.forEach(data => {
        worksheet.addRow(data);
      });
      
      // Format all data rows
      for (let i = 2; i <= processedData.length + 1; i++) {
        worksheet.getRow(i).eachCell((cell) => {
          cell.alignment = {
            vertical: 'middle'
          };
          cell.border = {
            top: { style: 'thin', color: { argb: 'D3D3D3' } },
            left: { style: 'thin', color: { argb: 'D3D3D3' } },
            bottom: { style: 'thin', color: { argb: 'D3D3D3' } },
            right: { style: 'thin', color: { argb: 'D3D3D3' } }
          };
        });
        
        // Set alternating row background color
        if (i % 2 === 0) {
          worksheet.getRow(i).eachCell((cell) => {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'F5F5F5' } // Light gray for even rows
            };
          });
        }
      }
      
      // Auto-size columns
      worksheet.columns.forEach(column => {
        let maxLength = 10;
        if (column && column.eachCell) {
          column.eachCell({ includeEmpty: false }, (cell) => {
            const columnLength = cell.value ? cell.value.toString().length : 0;
            if (columnLength > maxLength) {
              maxLength = columnLength;
            }
          });
          column.width = Math.min(30, Math.max(12, maxLength * 1.2));
        }
      });
      
      // Add context info and timestamp at the bottom
      const metaRow = worksheet.addRow(['']);
      const metaCell = metaRow.getCell(1);
      const contextInfo = isContainerView && containerContext 
        ? `Container: ${containerContext.containerNo}` 
        : 'All Manifests';
      metaCell.value = `${contextInfo} - Exported on ${formatDateString(new Date().toISOString())} - Total: ${processedData.length} manifests`;
      worksheet.mergeCells(metaRow.number, 1, metaRow.number, excelColumns.length);
      metaCell.font = {
        italic: true,
        color: { argb: '808080' } // Gray text
      };
      
      // Generate buffer
      const buffer = await workbook.xlsx.writeBuffer();
      
      // Generate filename
      const timestamp = formatTimestamp();
      const filename = isContainerView && containerContext 
        ? `container_${containerContext.containerNo}_manifests_${timestamp}.xlsx`
        : `manifests_export_${timestamp}.xlsx`;
      
      // Save file using FileSaver
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      saveAs(blob, filename);
      
      // Show success message
      toast.success(`Successfully exported ${processedData.length} manifests`);
    } catch (error) {
      console.error('Error exporting manifests:', error);
      toast.error('Failed to export manifests');
    }
  };

  // Export for RTrack with specific columns
  const exportForRTrack = async (manifestsToExport: Manifest[]) => {
    try {
      if (manifestsToExport.length === 0) {
        toast.warning('No manifests to export');
        return;
      }

      // Create a new workbook
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('RTrack Export');

      // Define RTrack specific columns
      const rtrackColumns = [
        { header: 'Region', key: 'region', width: 15 },
        { header: 'ID', key: 'id', width: 20 },
        { header: 'Tracking', key: 'trackingNo', width: 20 },
        { header: 'Cust', key: 'customerName', width: 25 },
        { header: 'Address', key: 'address', width: 40 },
        { header: 'Postal Code', key: 'postalCode', width: 15 },
        { header: 'Deliver On', key: 'deliveryDate', width: 15 },
        { header: 'Time Slot', key: 'timeSlot', width: 15 },
        { header: 'Phone', key: 'phoneNo', width: 18 },
        { header: 'For Driver Only', key: 'driverRemarks', width: 30 },
        { header: 'Vehicle Type', key: 'deliveryVehicle', width: 20 },
        { header: 'Pcs', key: 'pieces', width: 10 },
        { header: 'IB Pcs', key: 'inboundPieces', width: 10 },
        { header: 'Pallets', key: 'actualPalletsCount', width: 10 },
        { header: 'CBM', key: 'cbm', width: 10 },
        { header: 'For CS Only', key: 'remarks', width: 30 },
        { header: 'Container', key: 'containerNo', width: 18 },
        { header: 'Client', key: 'clientUsername', width: 18 }
      ];

      // Set columns
      worksheet.columns = rtrackColumns;

      // Style the header row (matching normal export)
      worksheet.getRow(1).eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '4F81BD' } // Blue background (same as normal export)
        };
        cell.font = {
          bold: true,
          color: { argb: 'FFFFFF' } // White text
        };
        cell.alignment = {
          vertical: 'middle',
          horizontal: 'center'
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });

      // Process manifest data for RTrack export
      const processedData = manifestsToExport.map(manifest => ({
        region: manifest.location || '', // Use location as region
        id: manifest.internalId || manifest.trackingNo || '',
        trackingNo: manifest.trackingNo || '',
        customerName: manifest.customerName || '',
        address: manifest.address || '',
        postalCode: manifest.postalCode || '',
        deliveryDate: manifest.deliveryDate ? formatDateOnly(manifest.deliveryDate) : '',
        timeSlot: formatTimeSlot(manifest.timeSlot || (manifest.deliveryDate ? getTimeSlotIdFromDate(manifest.deliveryDate) : null)),
        phoneNo: manifest.phoneNo || '',
        driverRemarks: manifest.driverRemarks || '',
        deliveryVehicle: manifest.deliveryVehicle || '',
        pieces: manifest.pieces || 0,
        inboundPieces: manifest.inboundPieces || 0,
        actualPalletsCount: manifest.actualPalletsCount || 0,
        cbm: manifest.cbm || 0,
        remarks: manifest.remarks || '',
        containerNo: manifest.container?.containerNo || '',
        clientUsername: manifest.client?.username || ''
      }));

      // Add data rows
      processedData.forEach(rowData => {
        worksheet.addRow(rowData);
      });

      // Format all data rows (matching normal export)
      for (let i = 2; i <= processedData.length + 1; i++) {
        worksheet.getRow(i).eachCell((cell) => {
          cell.alignment = {
            vertical: 'middle'
          };
          cell.border = {
            top: { style: 'thin', color: { argb: 'D3D3D3' } },
            left: { style: 'thin', color: { argb: 'D3D3D3' } },
            bottom: { style: 'thin', color: { argb: 'D3D3D3' } },
            right: { style: 'thin', color: { argb: 'D3D3D3' } }
          };
        });

        // Set alternating row background color (matching normal export)
        if (i % 2 === 0) {
          worksheet.getRow(i).eachCell((cell) => {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'F5F5F5' } // Light gray for even rows
            };
          });
        }
      }

      // Auto-size columns (matching normal export)
      worksheet.columns.forEach(column => {
        let maxLength = 10;
        if (column && column.eachCell) {
          column.eachCell({ includeEmpty: false }, (cell) => {
            const columnLength = cell.value ? cell.value.toString().length : 0;
            if (columnLength > maxLength) {
              maxLength = columnLength;
            }
          });
          column.width = Math.min(30, Math.max(12, maxLength * 1.2));
        }
      });

      // Generate buffer
      const buffer = await workbook.xlsx.writeBuffer();

      // Generate filename
      const timestamp = formatTimestamp();
      const filename = isContainerView && containerContext
        ? `rtrack_container_${containerContext.containerNo}_${timestamp}.xlsx`
        : `rtrack_export_${timestamp}.xlsx`;

      // Save file using FileSaver
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      saveAs(blob, filename);

      // Show success message
      toast.success(`Successfully exported ${processedData.length} manifests for RTrack`);
    } catch (error) {
      console.error('Error exporting manifests for RTrack:', error);
      toast.error('Failed to export manifests for RTrack');
    }
  };

  // Handlers for dialogs with corrected types
  const handleLabelDialogSave = (count: number) => {
    if (dialogProps.labelGeneration.manifest) {
      actions.handlePrintLabel(count);
    }
  };
  
  const handlePalletManagementComplete = async () => {
    // Traditional approach: Refresh the manifest data to get updated inbound pieces count
    // This will be called after selective updates have been processed
    console.log('🔄 ManifestList pallet operation complete callback - checking if full refresh is needed');

    // Only do full refresh if we haven't handled it via selective update
    if (!document.querySelector('[data-selective-update-handled]')) {
      console.log('📄 No selective update detected, performing full refresh');
      if (onRefresh) {
        // If parent provides a refresh function, use it
        await onRefresh();
      } else {
        // Otherwise, refresh the local data
        await handleRefresh();
      }
    } else {
      console.log('✅ Selective update already handled, skipping full refresh');
      // Remove the marker for next time
      document.querySelector('[data-selective-update-handled]')?.remove();
    }

    // DON'T auto-close the dialog - let user close it manually
    // This allows them to continue working with pallets if needed
    console.log('🔓 Keeping pallet management dialog open for continued use');
  };
  
  const handleDeliveryVehicleChange = async (vehicle: string | null) => {
    const manifest = dialogProps.deliveryVehicle.manifest;
    if (!manifest || !currentUser?.token) {
      throw new Error('Missing manifest or authentication token');
    }

    try {
      const response = await manifestService.updateManifestDeliveryVehicle(
        manifest.trackingNo,
        vehicle, // Pass null for auto-assignment, string for explicit assignment
        currentUser?.token || ''
      );

      if (response.success) {
        // Update the manifest in the local state directly (like ManifestManagement and ContainerDetail)
        setManifests(prevManifests =>
          prevManifests.map(m =>
            m.trackingNo === manifest.trackingNo
              ? {
                  ...m, // Keep all existing fields
                  deliveryVehicle: response.data.deliveryVehicle, // Update with new delivery vehicle
                  status: response.data.status // Status might be updated by backend
                }
              : m
          )
        );

        // Also call the prop callback if provided
        if (onManifestUpdate) {
          onManifestUpdate(response.data);
        }

        // Close the dialog
        closeDialog('deliveryVehicle');

        // Show success message
        console.log(`Delivery vehicle updated successfully for manifest ${manifest.trackingNo}`);
      } else {
        throw new Error(response.message || 'Failed to update delivery vehicle');
      }
    } catch (error: any) {
      console.error('Error updating delivery vehicle:', error);
      throw error; // Re-throw to let the dialog handle the error display
    }
  };

  // Add new state for bulk delivery date dialog
  const [bulkDeliveryDateDialogOpen, setBulkDeliveryDateDialogOpen] = useState(false);
  const [bulkDeliveryDateLoading, setBulkDeliveryDateLoading] = useState(false);
  const [bulkDeliveryDateError, setBulkDeliveryDateError] = useState<string | null>(null);
  const [bulkDeliveryDate, setBulkDeliveryDate] = useState<Date | null>(null);

  // Ref to store current manifests to avoid re-creating column handlers
  const manifestsRef = useRef<Manifest[]>(manifests);

  // Update ref when manifests change
  useEffect(() => {
    manifestsRef.current = manifests;
  }, [manifests]);

  // Add handler for bulk delivery date update
  const handleBulkDeliveryDateUpdate = () => {
    setBulkDeliveryDate(null);
    setBulkDeliveryDateDialogOpen(true);
  };

  // Add handler for bulk delivery date save
  const handleBulkDeliveryDateSave = async (date: Date | null, timeSlot: string | null) => {
    setBulkDeliveryDateLoading(true);
    setBulkDeliveryDateError(null);
    
    try {
      const trackingNos = selectedManifests.map(manifest => manifest.trackingNo);
      
      if (trackingNos.length === 0) {
        toast.warning('Please select at least one manifest');
        setBulkDeliveryDateLoading(false);
        return;
      }
      
      // Create a date object set to midnight for consistent handling
      let formattedDate = null;
      let formattedDisplayDate = null;
      
      if (date) {
        const userDate = new Date(date);
        userDate.setHours(0, 0, 0, 0);
        
        // Format the date as it should appear in the UI (dd MMM yyyy HH:mm)
        formattedDisplayDate = formatDate(userDate);
        console.log(`Formatted display date: ${formattedDisplayDate}`);
        
        // For the API, format date in the exact format the backend expects: YYYY-MM-DDTHH:MM:SS
        formattedDate = `${userDate.getFullYear()}-${String(userDate.getMonth() + 1).padStart(2, '0')}-${String(userDate.getDate()).padStart(2, '0')}T00:00:00`;
        console.log(`Formatted for API (backend format): ${formattedDate}`);
      }
      
      // Call the API to update delivery dates in bulk
      const response = await manifestService.updateBulkDeliveryDates(
        trackingNos,
        formattedDate,
        currentUser?.token || '',
        timeSlot
      );
      
      if (response.success) {
        // Check if there were any failures mentioned in the message
        const failureMatch = response.message?.match(/\((\d+) failed\)/);
        const failedCount = failureMatch ? parseInt(failureMatch[1]) : 0;
        
        if (failedCount > 0) {
          console.warn(`${failedCount} manifest updates failed`);
          toast.warning(`${failedCount} out of ${trackingNos.length} manifests could not be updated`);
        } else {
          const actionMessage = formattedDate ? 'updated' : 'cleared';
          toast.success(`Delivery date ${actionMessage} for ${trackingNos.length} manifest(s)`);
        }
      
      // Close the dialog
      setBulkDeliveryDateDialogOpen(false);
      
        // Reset the bulk delivery date
        setBulkDeliveryDate(null);
        
        // Clear selected manifests after successful update
        clearSelection();
      
      // Refresh the data
      handleRefresh();
      } else {
        toast.error(response.message || 'Failed to update delivery dates');
        console.error('API response indicated failure:', response.message);
      }
    } catch (error: any) {
      console.error('Error updating bulk delivery dates:', error);
      setBulkDeliveryDateError(error.response?.data?.message || 'Failed to update delivery dates');
      toast.error(error.response?.data?.message || 'An error occurred while updating delivery dates');
    } finally {
      setBulkDeliveryDateLoading(false);
    }
  };
  
  // Add state for single manifest delivery date edit
  const [singleDeliveryDate, setSingleDeliveryDate] = useState<Date | null>(null);
  
  // Update the single delivery date when dialog props change
  useEffect(() => {
    if (dialogProps.deliveryDate) {
      // Set the date regardless of whether it's null, undefined, or has a value
      setSingleDeliveryDate(dialogProps.deliveryDate.currentDate || null);
    }
  }, [dialogProps.deliveryDate]);

  // Add time slot state variables
  const [singleTimeSlot, setSingleTimeSlot] = useState<string | null>(null);
  const [bulkTimeSlot, setBulkTimeSlot] = useState<string | null>(null);

  // Update the single time slot when dialog props change
  useEffect(() => {
    if (dialogProps.deliveryDate) {
      // Set the time slot regardless of whether it's null, undefined, or has a value
      setSingleTimeSlot(dialogProps.deliveryDate.currentTimeSlot || null);
    }
  }, [dialogProps.deliveryDate]);

  // Update dialog state when the underlying manifest data changes
  useEffect(() => {
    if (dialogs.deliveryDate && dialogProps.deliveryDate?.trackingNo) {
      const currentManifest = manifests.find(m => m.trackingNo === dialogProps.deliveryDate.trackingNo);
      if (currentManifest) {
        // Update dialog state with the latest manifest data
        const latestDate = currentManifest.deliveryDate ? parseDateString(currentManifest.deliveryDate) : null;
        const latestTimeSlot = currentManifest.timeSlot || (latestDate ? getTimeSlotIdFromDate(latestDate) : null);

        setSingleDeliveryDate(latestDate);
        setSingleTimeSlot(latestTimeSlot);
      }
    }
  }, [manifests, dialogs.deliveryDate, dialogProps.deliveryDate?.trackingNo]);

  // Modify the renderDialogs function
  const renderDialogs = () => (
    <>
      {/* Confirmation dialog for single delete */}
      <Dialog open={dialogs.confirmDelete} onClose={() => closeDialog('confirmDelete')}>
        <DialogTitle>{dialogProps.confirmDelete.title || 'Confirm Delete'}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {dialogProps.confirmDelete.message || `Are you sure you want to delete this manifest?`}
          </DialogContentText>
          {dialogError && (
            <Typography color="error" variant="body2" sx={{ mt: 2 }}>
              {dialogError}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => closeDialog('confirmDelete')}>Cancel</Button>
          <Button 
            color="error" 
            onClick={actions.handleConfirmDelete}
            disabled={dialogLoading}
          >
            {dialogLoading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Confirmation dialog for bulk delete */}
      <Dialog open={dialogs.confirmBulkDelete} onClose={() => closeDialog('confirmBulkDelete')}>
        <DialogTitle>{dialogProps.confirmBulkDelete.title || 'Confirm Bulk Delete'}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {dialogProps.confirmBulkDelete.message || 
             `Are you sure you want to delete ${dialogProps.confirmBulkDelete.trackingNos.length} manifests?`}
          </DialogContentText>
          {dialogError && (
            <Typography color="error" variant="body2" sx={{ mt: 2 }}>
              {dialogError}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => closeDialog('confirmBulkDelete')}>Cancel</Button>
          <Button 
            color="error" 
            onClick={actions.handleConfirmBulkDelete}
            disabled={dialogLoading}
          >
            {dialogLoading ? <CircularProgress size={24} /> : 'Delete All'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Manifest label dialog */}
      {dialogs.labelGeneration && dialogProps.labelGeneration.manifest && (
        <ManifestLabelDialog
          open={dialogs.labelGeneration}
          manifest={dialogProps.labelGeneration.manifest}
          onClose={() => closeDialog('labelGeneration')}
          onGenerate={handleLabelDialogSave}
        />
      )}
      
      {/* Pallet management dialog */}
      {dialogs.palletManagement && dialogProps.palletManagement.manifest && (
        <PalletManagementDialog
          open={dialogs.palletManagement}
          manifest={dialogProps.palletManagement.manifest}
          onClose={() => closeDialog('palletManagement')}
          onPalletOperationComplete={handlePalletManagementComplete}
        />
      )}
      
      {/* Delivery date dialog */}
      {dialogs.deliveryDate && (
        <DeliveryDateDialog
          open={dialogs.deliveryDate}
          trackingNo={dialogProps.deliveryDate.trackingNo}
          internalId={dialogProps.deliveryDate.internalId}
          onClose={() => {
            closeDialog('deliveryDate');
            setSingleDeliveryDate(null);
            setSingleTimeSlot(null);
          }}
          onSave={(date, timeSlot) => actions.handleDeliveryDateChange(date, timeSlot)}
          deliveryDate={singleDeliveryDate}
          timeSlot={singleTimeSlot}
          onDateChange={setSingleDeliveryDate}
          onTimeSlotChange={setSingleTimeSlot}
          isEdit={true}
        />
      )}
      
      {/* Delivery vehicle dialog */}
      {dialogs.deliveryVehicle && dialogProps.deliveryVehicle.manifest && (
        <QuickDeliveryVehicleDialog
          open={dialogs.deliveryVehicle}
          trackingNo={dialogProps.deliveryVehicle.manifest.trackingNo}
          currentDeliveryVehicle={dialogProps.deliveryVehicle.manifest.deliveryVehicle || null}
          manifestWeight={dialogProps.deliveryVehicle.manifest.weight}
          manifestCbm={dialogProps.deliveryVehicle.manifest.cbm}
          onClose={() => closeDialog('deliveryVehicle')}
          onSave={handleDeliveryVehicleChange}
        />
      )}

      <DeliveryDateDialog
        open={bulkDeliveryDateDialogOpen}
        onClose={() => setBulkDeliveryDateDialogOpen(false)}
        onSave={(date, timeSlot) => handleBulkDeliveryDateSave(date, timeSlot)}
        deliveryDate={bulkDeliveryDate}
        timeSlot={bulkTimeSlot}
        onDateChange={setBulkDeliveryDate}
        onTimeSlotChange={setBulkTimeSlot}
        isEdit={false}
      />
    </>
  );
  
  return (
      <Box
        ref={manifestListRef}
        sx={{
        width: '100%', // Use responsive width
        display: 'flex',
        flexDirection: 'column',
        overflow: 'visible', // Allow horizontal overflow at browser level
      }}>
      {/* Main container with toolbar, column visibility, and data grid */}
      <Paper sx={{ 
        width: '100%', // Use responsive width
        borderRadius: '8px',
        boxShadow: 2,
        display: 'flex', 
        flexDirection: 'column',
        minHeight: '500px',
        position: 'relative',
        maxWidth: 'none', // Remove max width constraint to allow horizontal expansion
        mb: 2,
        overflow: 'visible', // Allow horizontal overflow
      }}>
      {/* Toolbar with action buttons */}
        <ManifestListToolbar
          ref={manifestTitleRef}
          selectedManifests={selectedManifests}
          totalManifests={filteredManifests.length}
          onExport={handleExportToExcel}
          onExportForRTrack={handleExportForRTrack}
          onPrintLabels={handleBulkLabelGeneration}
          onDelete={() => openDialog('confirmBulkDelete', {
            trackingNos: selectedManifests.map(m => m.trackingNo || ''),
            title: 'Confirm Bulk Delete',
            message: `Are you sure you want to delete ${selectedManifests.length} selected manifests?`
          })}
          onRefresh={handleRefresh}
          onBulkDeliveryDateUpdate={handleBulkDeliveryDateUpdate}
          isContainerView={isContainerView}
          onCreateManifest={onCreateManifest}
          onUploadManifest={onUploadManifest}
        />
      
        {/* Filters panel - Moved below toolbar */}
        <ManifestListFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          onResetFilters={resetFilters}
          locationZones={locationZones}
          vehicleTypes={vehicleTypes}
          loading={{
            locationsLoading,
            vehicleTypesLoading
          }}
          totals={totals}
        />
      
        {/* Search and Column visibility controls */}
        <Box sx={{
          p: 1, // Reduced padding from 1.5 to 1
          borderTop: '1px solid rgba(224, 224, 224, 0.5)', // Added border top
          borderBottom: '1px solid rgba(224, 224, 224, 1)',
          bgcolor: '#ffffff',
          overflow: 'visible', // Allow horizontal overflow
        }}>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          mb: 1,
          justifyContent: 'space-between',
          gap: 2
        }}>
          {/* Search field on the left - compact */}
          <Box sx={{ flex: '0 0 200px', minWidth: '300px' }}>
            <TextField
              fullWidth
              placeholder="Search..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" sx={{ fontSize: 16 }} />
                  </InputAdornment>
                ),
                endAdornment: filters.search ? (
                  <InputAdornment position="end">
                    <IconButton
                      size="small"
                      onClick={() => handleFilterChange('search', '')}
                      title="Clear search"
                      sx={{ p: 0.25 }}
                    >
                      <ClearIcon sx={{ fontSize: 14 }} />
                    </IconButton>
                  </InputAdornment>
                ) : null,
                sx: {
                  fontSize: '0.75rem',
                  py: 0.125,
                  minHeight: '32px'
                }
              }}
              sx={{ bgcolor: 'background.paper' }}
            />
          </Box>

          {/* Column visibility section on the right */}
          <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            <ViewColumnIcon color="primary" sx={{ mr: 1, fontSize: '1.2rem' }} />
            <Typography variant="subtitle2" sx={{ fontWeight: 'medium', color: 'text.secondary' }}>
              Column Visibility
            </Typography>
            <Typography variant="caption" sx={{ ml: 1, color: 'text.secondary', fontStyle: 'italic' }}>
              (Toggle columns to show/hide)
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
              <Button 
                size="small" 
                variant="text" 
                onClick={handleSelectAllColumns}
                sx={{ 
                  fontSize: '0.7rem', 
                  minWidth: 'auto', 
                  textTransform: 'none',
                  p: 0.5,
                  mr: 0.5
                }}
              >
                All
              </Button>
              <Button 
                size="small" 
                variant="text" 
                onClick={handleSelectNoColumns}
                sx={{ 
                  fontSize: '0.7rem', 
                  minWidth: 'auto', 
                  textTransform: 'none',
                  p: 0.5,
                  mr: 0.5
                }}
              >
                None
              </Button>
              <Button 
                size="small" 
                variant="text" 
                onClick={handleResetColumnVisibility}
                sx={{ 
                  fontSize: '0.7rem', 
                  minWidth: 'auto', 
                  textTransform: 'none',
                  p: 0.5
                }}
              >
                Reset
              </Button>
            </Box>
          </Box>
        </Box>
        
        <Box sx={{ 
          display: 'flex', 
          flexWrap: 'wrap', 
          gap: 0.5, 
          height: 'auto',
          maxHeight: '120px', 
          overflowY: 'auto',
          overflowX: 'hidden',
          scrollbarWidth: 'thin', // Firefox
          scrollbarColor: 'rgba(0,0,0,0.2) rgba(0,0,0,0.05)', // Firefox
          msOverflowStyle: 'auto', // IE and Edge
          '&::-webkit-scrollbar': {
            width: '10px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'rgba(0,0,0,0.03)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: 'rgba(0,0,0,0.15)',
            borderRadius: '4px',
            '&:hover': {
              background: 'rgba(0,0,0,0.25)',
            },
          }
        }}
        className="column-visibility-container"
        >
          {[
            'location', 'internalId', 'trackingNo', 'customerName', 'address', 'postalCode',
            'deliveryDate', 'timeSlot', 'phoneNo', 'driverRemarks', 'driver', 'status', 'deliveryVehicle',
            'pieces', 'inboundPieces', 'actualPalletsCount', 'cbm', 'remarks', 'weight',
            'deliveredDate', 'createdDate', 'country', 'container', 'client'
          ].map((column) => {
            // Only show toggle for columns that exist in visibleColumns
            if (!(column in visibleColumns)) return null;

            const isVisible = visibleColumns[column];

            // Map column field names to their actual header names
            const columnHeaderMap: Record<string, string> = {
              location: 'Region',
              internalId: 'ID',
              trackingNo: 'Tracking',
              customerName: 'Cust',
              address: 'Address',
              postalCode: 'Postal Code',
              deliveryDate: 'Deliver On',
              timeSlot: 'Time Slot',
              phoneNo: 'Phone',
              driverRemarks: 'For Driver Only',
              driver: 'Driver',
              status: 'Status',
              deliveryVehicle: 'Vehicle Type',
              pieces: 'Pcs',
              inboundPieces: 'IB Pcs',
              actualPalletsCount: 'Pallets',
              cbm: 'CBM',
              remarks: 'For CS Only',
              weight: 'Weight (kg)',
              deliveredDate: 'POD',
              createdDate: 'Created On',
              country: 'Country',
              container: 'Container',
              client: 'Client'
            };

            const displayName = columnHeaderMap[column] || column.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()).trim();

            return (
              <Button
                key={column}
                size="small"
                variant={isVisible ? "contained" : "outlined"}
                color={isVisible ? "primary" : "inherit"}
                onClick={() => handleColumnToggle(column)}
                sx={{
                  borderRadius: 3,
                  px: 0.75, // Better horizontal padding for readability
                  py: 0.25, // Small vertical padding
                  minWidth: '60px', // Larger minimum width
                  maxWidth: '100px', // Larger maximum width
                  height: '28px', // Better height for readability
                  fontSize: '0.7rem', // Larger, more readable font
                  textTransform: 'none',
                  lineHeight: 1.2, // Better line height
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: isVisible ? 0 : 0,
                  backgroundColor: isVisible ? 'primary.light' : 'transparent',
                  color: isVisible ? 'white' : 'text.secondary',
                  border: isVisible ? 'none' : '1px solid rgba(0,0,0,0.12)',
                  '&:hover': {
                    backgroundColor: isVisible ? 'primary.main' : 'rgba(0,0,0,0.04)',
                    boxShadow: 0
                  }
                }}
              >
                {displayName}
              </Button>
            );
          })}
        </Box>
        </Box>
      
      {/* Main data grid */}
        <Box sx={{ 
          display: 'flex',
          flexDirection: 'column',
          overflow: 'visible', // Allow horizontal overflow
          flexGrow: 1,
          position: 'relative',
          width: '100%',
        }}>
        <ManifestListGrid 
          manifests={filteredManifests}
          columns={columns}
          visibleColumns={visibleColumns}
          loading={loading}
          error={error}
          onSelectionChange={handleSelectionChange}
          onColumnVisibilityChange={handleColumnVisibilityChange}
          onEdit={columnActionHandlers.onEdit}
          onDelete={columnActionHandlers.onDelete}
          onChangeStatus={(trackingNo: string, status: string) => 
            openDialog('changeStatus', { trackingNo, status: status as ManifestStatus })
          }
          containerContext={containerContext}
        />
        </Box>
      </Paper>
      
      {/* Render all dialog components */}
      {renderDialogs()}

      {/* Floating Action Button (FAB) - Optimized for performance */}
      <Zoom in={showScrollToTop} timeout={300}>
        <Fab
          size="small"
          color="primary"
          aria-label="scroll to above Manifest List title"
          onClick={handleScrollToTop}
          sx={{
            position: 'fixed',
            bottom: 20,
            right: 20,
            zIndex: 1000, // Higher z-index to ensure it's always on top
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)', // Smooth transition
            transform: showScrollToTop ? 'scale(1)' : 'scale(0)',
            opacity: showScrollToTop ? 1 : 0,
            boxShadow: '0 2px 4px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.08)',
            width: '40px',
            height: '40px',
            '&:hover': {
              transform: 'scale(1.1)',
              boxShadow: '0 4px 8px rgba(0,0,0,0.15), 0 2px 4px rgba(0,0,0,0.1)',
            },
            '&:active': {
              transform: 'scale(0.95)',
            },
            // Improve performance by using will-change
            willChange: 'transform, opacity',
          }}
        >
          <KeyboardArrowUpIcon fontSize="small" />
        </Fab>
      </Zoom>
    </Box>
  );
};

export default ManifestList; 