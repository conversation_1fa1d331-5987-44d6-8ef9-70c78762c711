import React, { forwardRef } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  IconButton,
  Chip,
  Tooltip,
  Divider
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import DeleteIcon from '@mui/icons-material/Delete';
import GetAppIcon from '@mui/icons-material/GetApp';
import AddIcon from '@mui/icons-material/Add';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';
import { Container } from '../../types/Container';

interface ContainerListToolbarProps {
  selectedContainers: Container[];
  totalContainers: number;
  onRefresh: () => void;
  onCreateContainer?: () => void;
  onUploadContainer?: () => void;
  onDelete?: (containerNos: string[]) => void;
}

const ContainerListToolbar = forwardRef<HTMLDivElement, ContainerListToolbarProps>(({
  selectedContainers,
  totalContainers,
  onRefresh,
  onCreateContainer,
  onUploadContainer,
  onDelete
}, ref) => {
  const hasSelection = selectedContainers.length > 0;

  const handleBulkDelete = () => {
    if (onDelete && hasSelection) {
      const containerNos = selectedContainers.map(c => c.containerNo);
      onDelete(containerNos);
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      borderBottom: '1px solid rgba(224, 224, 224, 1)',
      bgcolor: '#ffffff',
      borderTopLeftRadius: '8px',
      borderTopRightRadius: '8px',
    }}>
      {/* First row with title and refresh button */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        pl: { sm: 2 },
        pr: { xs: 1, sm: 1 },
        py: 1,
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1.5 }}>
          <Typography
            ref={ref}
            variant="h6"
            component="div"
            sx={{ mr: 1 }}
          >
            Container List
          </Typography>
          <Chip
            label={`Total: ${totalContainers}`}
            size="small"
            color="primary"
            variant="outlined"
            sx={{
              height: '22px',
              fontSize: '0.75rem',
              fontWeight: 500,
              '& .MuiChip-label': { px: 1 }
            }}
          />

          {hasSelection && (
            <Chip
              label={`${selectedContainers.length} selected`}
              size="small"
              color="primary"
              sx={{
                height: '22px',
                fontSize: '0.75rem',
                fontWeight: 500,
                '& .MuiChip-label': { px: 1 }
              }}
            />
          )}
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="Refresh data">
            <IconButton
              size="small"
              onClick={onRefresh}
              sx={{
                color: 'primary.main',
                '&:hover': {
                  bgcolor: 'rgba(25, 118, 210, 0.08)',
                }
              }}
            >
              <RefreshIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Second row with action buttons */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        pl: { sm: 2 },
        pr: { xs: 1, sm: 1 },
        pb: 1,
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
          {/* Create container button */}
          {onCreateContainer && (
            <Tooltip title="Create new container">
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                size="small"
                onClick={onCreateContainer}
                sx={{
                  borderRadius: 2,
                  minWidth: 'auto',
                  px: 2,
                  py: 0.5,
                  fontSize: '0.75rem',
                  whiteSpace: 'nowrap',
                  fontWeight: 'bold',
                  boxShadow: 2,
                }}
              >
                Create Container
              </Button>
            </Tooltip>
          )}

          {/* Upload containers button */}
          {onUploadContainer && (
            <Tooltip title="Upload containers from Excel">
              <Button
                variant="outlined"
                color="secondary"
                startIcon={<UploadFileIcon />}
                size="small"
                onClick={onUploadContainer}
                sx={{
                  borderRadius: 2,
                  minWidth: 'auto',
                  px: 2,
                  py: 0.5,
                  fontSize: '0.75rem',
                  whiteSpace: 'nowrap',
                  fontWeight: 'bold',
                }}
              >
                Upload
              </Button>
            </Tooltip>
          )}
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Note: Bulk delete temporarily disabled due to selection model compatibility issues */}
          {/* Will be re-enabled once DataGrid selection is properly configured */}
        </Box>
      </Box>
    </Box>
  );
});

ContainerListToolbar.displayName = 'ContainerListToolbar';

export default ContainerListToolbar;
