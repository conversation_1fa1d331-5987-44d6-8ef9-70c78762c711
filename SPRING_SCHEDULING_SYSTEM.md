# Spring Scheduling System in WMS

## Overview

The WMS system uses Spring's built-in scheduling framework to execute background tasks automatically. The scheduling system is enabled through the `@EnableScheduling` annotation and uses various scheduling strategies for different types of tasks.

## Core Configuration

### 1. Enabling Scheduling

```java
@SpringBootApplication
@EnableScheduling  // ← This enables the scheduling framework
public class WmsApplication {
    // Application configuration
}
```

### 2. Timezone Configuration

All scheduled tasks run in **Singapore timezone** (`Asia/Singapore`):

```java
@PostConstruct
public void init() {
    TimeZone.setDefault(TimeZone.getTimeZone("Asia/Singapore"));
    logger.info("Application timezone set to: {}", TimeZone.getDefault().getID());
}
```

## Current Scheduled Tasks

### 1. Manifest Status Updates (Daily)

**Location:** `ManifestStatusServiceImpl.scheduledDeliveryDateStatusUpdate()`

```java
@Scheduled(cron = "0 0 0 * * *") // Runs at midnight every day
@Transactional
public void scheduledDeliveryDateStatusUpdate()
```

**Purpose:** Updates manifest statuses based on delivery date proximity
- `PENDING_DELIVER` → `READY_TO_DELIVER` (≤ 1 day until delivery)
- `READY_TO_DELIVER` → `PENDING_DELIVER` (≥ 2 days until delivery)

**Execution Time:** Daily at 00:00:00 Singapore time

### 2. Authentication Cache Cleanup (Every 10 minutes)

**Location:** `AuthTokenFilter.clearExpiredCacheEntries()`

```java
@Scheduled(fixedRate = 600000) // Every 10 minutes (600,000 ms)
public void clearExpiredCacheEntries()
```

**Purpose:** Removes expired authentication cache entries to free memory

### 3. JWT Token Cleanup (Every Hour)

**Location:** `InMemoryJwtTokenStore.cleanupExpiredTokens()`

```java
@Scheduled(fixedRate = 3600000) // Every hour (3,600,000 ms)
public void cleanupExpiredTokens()
```

**Purpose:** Removes expired JWT tokens from in-memory store

## Scheduling Strategies

### 1. Cron Expressions

**Format:** `second minute hour day month weekday`

**Example:** `@Scheduled(cron = "0 0 0 * * *")`
- `0` - Second (0)
- `0` - Minute (0) 
- `0` - Hour (0 = midnight)
- `*` - Day (every day)
- `*` - Month (every month)
- `*` - Weekday (every day of week)

**Alternative Options (commented in code):**
```java
@Scheduled(cron = "0 0 6 * * *")      // Every 6 hours
@Scheduled(cron = "0 0 8,20 * * *")   // Twice daily at 8 AM and 8 PM
```

### 2. Fixed Rate

**Format:** `@Scheduled(fixedRate = milliseconds)`

**Examples:**
- `fixedRate = 600000` - Every 10 minutes
- `fixedRate = 3600000` - Every 1 hour

**Behavior:** Executes at fixed intervals regardless of previous execution time

### 3. Fixed Delay

**Format:** `@Scheduled(fixedDelay = milliseconds)`

**Behavior:** Waits for specified delay after previous execution completes

## Thread Pool Configuration

### Default Configuration

Spring uses a **single-threaded executor** by default for scheduled tasks:
- **Pool Size:** 1 thread
- **Behavior:** Tasks execute sequentially, not concurrently
- **Isolation:** Each task runs in its own transaction

### Custom Configuration (Not Currently Implemented)

To customize the thread pool, you would create a configuration like:

```java
@Configuration
@EnableScheduling
public class SchedulingConfig implements SchedulingConfigurer {
    
    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(5);
        scheduler.setThreadNamePrefix("wms-scheduled-");
        scheduler.initialize();
        taskRegistrar.setScheduler(scheduler);
    }
}
```

## Transaction Management

### Transactional Behavior

```java
@Scheduled(cron = "0 0 0 * * *")
@Transactional  // ← Each scheduled task runs in its own transaction
public void scheduledDeliveryDateStatusUpdate()
```

**Benefits:**
- **Atomicity:** All database operations succeed or fail together
- **Isolation:** Concurrent operations don't interfere
- **Rollback:** Automatic rollback on exceptions
- **Consistency:** Database remains in valid state

### Database Connection Pool

**HikariCP Configuration (Production):**
```properties
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=300000
```

## Error Handling

### Exception Isolation

- Each scheduled task runs independently
- Exceptions in one task don't affect others
- Failed tasks are logged but don't stop the scheduler

### Logging

All scheduled tasks include comprehensive logging:

```java
logger.info("Starting scheduled delivery date status update task...");
// ... task execution ...
logger.info("Scheduled delivery date status update completed. Updated {} out of {} manifests", 
           updatedCount, manifestsToCheck.size());
```

## Manual Triggers

### API Endpoints

Some scheduled tasks can be triggered manually:

```java
@PostMapping("/api/manifests/update-delivery-statuses")
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
public ResponseEntity<ApiResponse<Integer>> updateDeliveryStatuses()
```

**Usage:** Allows administrators to trigger delivery status updates on-demand

## Monitoring and Debugging

### Log Messages

**Startup:**
```
WMS Application Starting with profile: prod
Timezone: Asia/Singapore
```

**Scheduled Execution:**
```
Starting scheduled delivery date status update task...
Found 25 manifests with delivery dates to check
Scheduled delivery date status update completed. Updated 3 out of 25 manifests
```

**Cache Cleanup:**
```
Clearing expired authentication cache entries
Removed 5 expired cache entries, 20 remaining
```

### Performance Considerations

1. **Single Thread:** Default configuration prevents concurrent execution
2. **Database Load:** Scheduled tasks use connection pool efficiently
3. **Memory Management:** Regular cleanup tasks prevent memory leaks
4. **Transaction Scope:** Each task has bounded transaction lifetime

## Best Practices

### 1. Task Design
- Keep tasks lightweight and focused
- Use appropriate transaction boundaries
- Include comprehensive error handling
- Log execution details for monitoring

### 2. Scheduling Strategy
- Use cron expressions for time-based tasks
- Use fixed rates for maintenance tasks
- Consider timezone implications
- Plan for system downtime scenarios

### 3. Resource Management
- Monitor database connection usage
- Implement proper cleanup procedures
- Use appropriate transaction isolation levels
- Handle long-running tasks carefully

This scheduling system ensures reliable, automated execution of critical background tasks while maintaining system performance and data consistency.
