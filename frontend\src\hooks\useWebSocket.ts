import { useEffect, useState, useCallback, useRef } from 'react';
import { getWebSocketService, ManifestStatusUpdate, SystemNotification } from '../services/websocket.service';

interface UseWebSocketOptions {
  autoConnect?: boolean;
  baseUrl?: string;
  containerNo?: string; // Subscribe to container-specific updates
}

interface UseWebSocketReturn {
  connected: boolean;
  connect: () => Promise<void>;
  disconnect: () => void;
  manifestStatusUpdates: ManifestStatusUpdate[];
  systemNotifications: SystemNotification[];
  clearManifestUpdates: () => void;
  clearSystemNotifications: () => void;
  subscribeToContainer: (containerNo: string) => void;
}

export const useWebSocket = (options: UseWebSocketOptions = {}): UseWebSocketReturn => {
  const { autoConnect = true, baseUrl, containerNo } = options;
  
  const [connected, setConnected] = useState(false);
  const [manifestStatusUpdates, setManifestStatusUpdates] = useState<ManifestStatusUpdate[]>([]);
  const [systemNotifications, setSystemNotifications] = useState<SystemNotification[]>([]);
  
  const webSocketService = useRef(getWebSocketService(baseUrl));
  const listenersRegistered = useRef(false);

  // Event handlers
  const handleManifestStatusUpdate = useCallback((update: ManifestStatusUpdate) => {
    setManifestStatusUpdates(prev => [update, ...prev].slice(0, 100)); // Keep last 100 updates
  }, []);

  const handleSystemNotification = useCallback((notification: SystemNotification) => {
    setSystemNotifications(prev => [notification, ...prev].slice(0, 50)); // Keep last 50 notifications
  }, []);

  const handleConnectionChange = useCallback((isConnected: boolean) => {
    setConnected(isConnected);
  }, []);

  // Register event listeners
  useEffect(() => {
    if (!listenersRegistered.current) {
      const service = webSocketService.current;
      
      service.addManifestStatusListener(handleManifestStatusUpdate);
      service.addSystemNotificationListener(handleSystemNotification);
      service.addConnectionListener(handleConnectionChange);
      
      listenersRegistered.current = true;
      
      // Cleanup function
      return () => {
        service.removeManifestStatusListener(handleManifestStatusUpdate);
        service.removeSystemNotificationListener(handleSystemNotification);
        service.removeConnectionListener(handleConnectionChange);
        listenersRegistered.current = false;
      };
    }
  }, [handleManifestStatusUpdate, handleSystemNotification, handleConnectionChange]);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      webSocketService.current.connect().catch(error => {
        console.error('Failed to auto-connect to WebSocket:', error);
      });
    }

    // Subscribe to container-specific updates if provided
    if (containerNo && connected) {
      webSocketService.current.subscribeToContainer(containerNo);
    }

    // Cleanup on unmount
    return () => {
      if (autoConnect) {
        webSocketService.current.disconnect();
      }
    };
  }, [autoConnect, containerNo, connected]);

  // Connection methods
  const connect = useCallback(async () => {
    try {
      await webSocketService.current.connect();
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
      throw error;
    }
  }, []);

  const disconnect = useCallback(() => {
    webSocketService.current.disconnect();
  }, []);

  // Utility methods
  const clearManifestUpdates = useCallback(() => {
    setManifestStatusUpdates([]);
  }, []);

  const clearSystemNotifications = useCallback(() => {
    setSystemNotifications([]);
  }, []);

  const subscribeToContainer = useCallback((containerNumber: string) => {
    if (connected) {
      webSocketService.current.subscribeToContainer(containerNumber);
    }
  }, [connected]);

  return {
    connected,
    connect,
    disconnect,
    manifestStatusUpdates,
    systemNotifications,
    clearManifestUpdates,
    clearSystemNotifications,
    subscribeToContainer,
  };
};

export default useWebSocket;
