
# WebSocket Real-time Updates - Testing Guide

## Implementation Summary

✅ **Backend Implementation Complete:**
- WebSocket configuration with STOMP messaging
- Real-time notification service
- Event-driven delivery date change handling
- Async processing configuration
- Integration with manifest status service
- Test endpoint for WebSocket notifications

✅ **Frontend Implementation Complete:**
- WebSocket service with reconnection logic
- React hook for easy integration
- Status indicator component in header
- Real-time updates in ManifestList component
- Toast notifications for status changes

## Testing the Implementation

### 1. Start the Application

**Backend:**
```bash
cd backend
./mvnw spring-boot:run
```

**Frontend:**
```bash
cd frontend
npm start
```

### 2. Verify WebSocket Connection

1. **Open the application** in your browser (http://localhost:5173)
2. **Login** with your credentials
3. **Check the header** - You should see a WebSocket status indicator
4. **Green chip** = Connected, **Red chip** = Disconnected
5. **Click the notification icon** to see real-time update history

### 3. Test Real-time Notifications

#### Method 1: Test Endpoint
1. **Open browser developer tools** (F12)
2. **Go to Network tab** and filter by "WS" to see WebSocket traffic
3. **Call the test endpoint:**
   ```bash
   curl -X POST http://localhost:8080/api/manifests/test-websocket \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json"
   ```
4. **Check the frontend** - You should see a system notification appear

#### Method 2: Manual Status Update Trigger
1. **Navigate to** Containers or Manifests page
2. **Click the "Update Delivery Statuses" button** (if available)
3. **Watch for real-time updates** in the manifest list

#### Method 3: Scheduled Task (Wait for Midnight)
1. **Set up manifests** with delivery dates for tomorrow
2. **Wait for midnight Singapore time** (or change the cron expression for testing)
3. **Watch for automatic status updates**

### 4. Test Event-Driven Updates

1. **Open a manifest detail page**
2. **Change the delivery date** to tomorrow
3. **Save the changes**
4. **Watch for immediate status update** via WebSocket

### 5. Test Container-Specific Updates

1. **Open a container detail page**
2. **The WebSocket will subscribe** to container-specific updates
3. **Any manifest status changes** in that container will appear in real-time

## Expected Behaviors

### ✅ Connection Status
- **Green "Connected" chip** in header when WebSocket is active
- **Red "Disconnected" chip** with reconnect button when connection fails
- **Automatic reconnection** with exponential backoff

### ✅ Real-time Updates
- **Toast notifications** appear when manifest statuses change
- **Manifest list updates** automatically without page refresh
- **Status chips** reflect current status immediately
- **Update counter** in notification badge increases

### ✅ Status Transitions
- **PENDING_DELIVER → READY_TO_DELIVER** when delivery date ≤ 1 day away
- **READY_TO_DELIVER → PENDING_DELIVER** when delivery date ≥ 2 days away
- **Immediate updates** when delivery dates are changed manually

## Debugging

### Backend Logs
Look for these log messages:
```
INFO: Starting scheduled delivery date status update task at 2024-01-15 (Singapore time)
INFO: Checking delivery date for manifest TRK001: today=2024-01-15, deliveryDate=2024-01-16, days until delivery=1
INFO: Manifest TRK001 should be READY_TO_DELIVER (delivery within 1 day)
INFO: Broadcasted manifest status update via WebSocket: TRK001 PENDING_DELIVER -> READY_TO_DELIVER
```

### Frontend Console
Look for these console messages:
```
STOMP Debug: Connected to server
WebSocket connected: CONNECTED
Subscribed to WebSocket topics
Received manifest status update: {type: "manifest_status_update", ...}
🔄 ManifestList: Applied real-time status update via WebSocket
```

### Network Tab
- **WebSocket connection** should show as "101 Switching Protocols"
- **STOMP frames** should be visible in the WebSocket messages
- **Heartbeat messages** should appear every 4 seconds

## Troubleshooting

### Connection Issues

**Problem:** WebSocket shows "Disconnected"
**Solutions:**
1. Check if backend is running on port 8080
2. Verify CORS configuration allows WebSocket connections
3. Check browser console for connection errors
4. Try manual reconnection using the reconnect button

**Problem:** No real-time updates received
**Solutions:**
1. Verify WebSocket connection is established
2. Check if user has proper authentication
3. Look for STOMP subscription confirmations in console
4. Test with the manual test endpoint

### Status Update Issues

**Problem:** Scheduled updates not working
**Solutions:**
1. Check if scheduled task is enabled
2. Verify timezone configuration (Asia/Singapore)
3. Look for scheduled task execution logs
4. Use manual trigger endpoint for testing

**Problem:** Event-driven updates not working
**Solutions:**
1. Verify async configuration is enabled
2. Check if delivery date change events are being published
3. Look for event listener execution logs
4. Test with manual delivery date changes

## API Endpoints for Testing

### Manual Triggers
```bash
# Trigger delivery status updates
POST /api/manifests/update-delivery-statuses

# Test WebSocket notification
POST /api/manifests/test-websocket

# Debug delivery date calculations
GET /api/manifests/debug-delivery-dates
```

### Example Responses
```json
// Debug delivery dates response
{
  "success": true,
  "data": {
    "currentDate": "2024-01-15",
    "timezone": "Asia/Singapore",
    "totalManifestsWithDeliveryDates": 5,
    "manifests": [
      {
        "trackingNo": "TRK001",
        "currentStatus": "PENDING_DELIVER",
        "deliveryDate": "2024-01-16",
        "daysUntilDelivery": 1,
        "shouldBeStatus": "READY_TO_DELIVER"
      }
    ]
  }
}
```

## Performance Considerations

### Connection Management
- **Single WebSocket connection** per browser tab
- **Automatic cleanup** when component unmounts
- **Efficient message routing** using topics

### Message Volume
- **Status updates only** when changes occur
- **Container-specific subscriptions** reduce unnecessary traffic
- **Message history limited** to last 100 updates

### Resource Usage
- **Lightweight STOMP protocol**
- **Async processing** prevents blocking
- **Thread pool configuration** handles concurrent events

## Next Steps

1. **Monitor production usage** and adjust thread pool sizes if needed
2. **Add user-specific notifications** for personalized updates
3. **Implement message persistence** for offline users
4. **Add performance metrics** for WebSocket health monitoring
5. **Consider push notifications** for mobile users

The implementation provides a solid foundation for real-time manifest status updates while maintaining system reliability and performance.
