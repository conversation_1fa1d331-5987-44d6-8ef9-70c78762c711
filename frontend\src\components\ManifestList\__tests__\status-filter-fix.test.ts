/**
 * Test to verify that status counts are calculated correctly when status filters are applied
 * 
 * Issue: When applying a status filter, the status chip counts were showing 0 for all other statuses
 * because the counts were calculated from filtered manifests instead of original manifests.
 * 
 * Fix: Calculate status counts from original manifests while calculating other totals from filtered manifests.
 */

import { ManifestStatus } from '../../../types/manifest';

// Mock manifest data for testing
const mockManifests = [
  { trackingNo: 'TRK001', status: ManifestStatus.CREATED, pieces: 10, cbm: 1.5 },
  { trackingNo: 'TRK002', status: ManifestStatus.CREATED, pieces: 5, cbm: 0.8 },
  { trackingNo: 'TRK003', status: ManifestStatus.INBOUNDED_TO_WAREHOUSE, pieces: 8, cbm: 1.2 },
  { trackingNo: 'TRK004', status: ManifestStatus.READY_TO_DELIVER, pieces: 12, cbm: 2.0 },
  { trackingNo: 'TRK005', status: ManifestStatus.DELIVERED, pieces: 6, cbm: 0.9 },
  { trackingNo: 'TRK006', status: ManifestStatus.DELIVERED, pieces: 15, cbm: 2.5 },
];

// Simulate the old (incorrect) implementation
const calculateTotalsOldWay = (manifests: any[]) => {
  const result = manifests.reduce((totals, manifest) => {
    // This was the problem: status counts calculated from filtered manifests
    const status = manifest.status;
    totals.byStatus[status] = (totals.byStatus[status] || 0) + 1;

    return {
      totalPieces: totals.totalPieces + (manifest.pieces || 0),
      totalCBM: totals.totalCBM + (manifest.cbm || 0),
      byStatus: totals.byStatus
    };
  }, {
    totalPieces: 0,
    totalCBM: 0,
    byStatus: {} as Record<ManifestStatus, number>
  });

  return result;
};

// Simulate the new (correct) implementation
const calculateTotalsNewWay = (filteredManifests: any[], allManifests: any[]) => {
  // Calculate other totals from filtered manifests
  const filteredTotals = filteredManifests.reduce((totals, manifest) => {
    return {
      totalPieces: totals.totalPieces + (manifest.pieces || 0),
      totalCBM: totals.totalCBM + (manifest.cbm || 0),
    };
  }, {
    totalPieces: 0,
    totalCBM: 0,
  });

  // Calculate status counts from ALL manifests (not filtered)
  const statusCounts = allManifests.reduce((acc, manifest) => {
    const status = manifest.status;
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {} as Record<ManifestStatus, number>);

  return {
    ...filteredTotals,
    byStatus: statusCounts
  };
};

describe('Status Filter Fix', () => {
  test('should demonstrate the problem with old implementation', () => {
    // Filter to only show CREATED manifests
    const filteredManifests = mockManifests.filter(m => m.status === ManifestStatus.CREATED);
    
    // Calculate totals using old (incorrect) method
    const totalsOldWay = calculateTotalsOldWay(filteredManifests);
    
    // Problem: Only CREATED status has count, others are missing
    expect(totalsOldWay.byStatus[ManifestStatus.CREATED]).toBe(2);
    expect(totalsOldWay.byStatus[ManifestStatus.DELIVERED]).toBeUndefined(); // This is the problem!
    expect(totalsOldWay.byStatus[ManifestStatus.READY_TO_DELIVER]).toBeUndefined();
    
    // Other totals are correctly calculated from filtered data
    expect(totalsOldWay.totalPieces).toBe(15); // 10 + 5 from CREATED manifests only
    expect(totalsOldWay.totalCBM).toBe(2.3); // 1.5 + 0.8 from CREATED manifests only
  });

  test('should show the fix with new implementation', () => {
    // Filter to only show CREATED manifests
    const filteredManifests = mockManifests.filter(m => m.status === ManifestStatus.CREATED);
    
    // Calculate totals using new (correct) method
    const totalsNewWay = calculateTotalsNewWay(filteredManifests, mockManifests);
    
    // Fix: All status counts are preserved from original data
    expect(totalsNewWay.byStatus[ManifestStatus.CREATED]).toBe(2);
    expect(totalsNewWay.byStatus[ManifestStatus.DELIVERED]).toBe(2); // Now correctly shows 2!
    expect(totalsNewWay.byStatus[ManifestStatus.READY_TO_DELIVER]).toBe(1); // Now correctly shows 1!
    expect(totalsNewWay.byStatus[ManifestStatus.INBOUNDED_TO_WAREHOUSE]).toBe(1); // Now correctly shows 1!
    
    // Other totals are still correctly calculated from filtered data
    expect(totalsNewWay.totalPieces).toBe(15); // 10 + 5 from CREATED manifests only
    expect(totalsNewWay.totalCBM).toBe(2.3); // 1.5 + 0.8 from CREATED manifests only
  });

  test('should work correctly when no filters are applied', () => {
    // No filtering - all manifests
    const filteredManifests = mockManifests;
    
    // Both methods should give same results when no filtering
    const totalsOldWay = calculateTotalsOldWay(filteredManifests);
    const totalsNewWay = calculateTotalsNewWay(filteredManifests, mockManifests);
    
    expect(totalsOldWay.byStatus).toEqual(totalsNewWay.byStatus);
    expect(totalsOldWay.totalPieces).toBe(totalsNewWay.totalPieces);
    expect(totalsOldWay.totalCBM).toBe(totalsNewWay.totalCBM);
    
    // Verify all counts are correct
    expect(totalsNewWay.byStatus[ManifestStatus.CREATED]).toBe(2);
    expect(totalsNewWay.byStatus[ManifestStatus.DELIVERED]).toBe(2);
    expect(totalsNewWay.byStatus[ManifestStatus.READY_TO_DELIVER]).toBe(1);
    expect(totalsNewWay.byStatus[ManifestStatus.INBOUNDED_TO_WAREHOUSE]).toBe(1);
    
    // Total pieces: 10 + 5 + 8 + 12 + 6 + 15 = 56
    expect(totalsNewWay.totalPieces).toBe(56);
    // Total CBM: 1.5 + 0.8 + 1.2 + 2.0 + 0.9 + 2.5 = 8.9
    expect(totalsNewWay.totalCBM).toBe(8.9);
  });
});
