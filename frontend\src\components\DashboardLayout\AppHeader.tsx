import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  IconButton,
  Button,
  Avatar,
  useTheme
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import LogoutIcon from '@mui/icons-material/Logout';

import { useAuth } from '../../contexts/AuthContext';
import { useLayoutContext } from './LayoutContext';
import WebSocketStatus from '../WebSocketStatus';

const AppHeader: React.FC = () => {
  const { currentUser, logout } = useAuth();
  const { drawerWidth, mobileOpen, setMobileOpen } = useLayoutContext();
  const navigate = useNavigate();
  const theme = useTheme();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <AppBar
      position="fixed"
      sx={{
        width: { 
          xs: '100%',
          sm: `calc(100% - ${drawerWidth}px)`
        },
        ml: { 
          xs: 0,
          sm: `${drawerWidth}px`
        },
        boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
        borderRadius: 0,
        transition: theme.transitions.create(['width', 'margin', 'left'], {
          easing: theme.transitions.easing.easeInOut,
          duration: theme.transitions.duration.standard,
        }),
      }}
    >
      <Toolbar variant="dense" sx={{
        width: '100%',
        minWidth: '100%',
        display: 'flex',
        justifyContent: 'space-between'
      }}>
        <IconButton
          color="inherit"
          aria-label="open drawer"
          edge="start"
          onClick={handleDrawerToggle}
          sx={{ mr: 2, display: { sm: 'none' } }}
        >
          <MenuIcon />
        </IconButton>
        <Typography variant="subtitle1" noWrap component="div" sx={{ flexGrow: 1, fontSize: '1rem', fontWeight: 500 }}>
          Fukuyama Logistics Warehouse Management System
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {/* WebSocket Status Indicator */}
          <Box sx={{ mr: 2 }}>
            <WebSocketStatus showNotifications={true} showDetails={false} />
          </Box>

          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            mr: 2,
            p: 1,
            borderRadius: 0,
            bgcolor: 'rgba(255, 255, 255, 0.15)',
            transition: 'background-color 0.2s',
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 0.25)'
            }
          }}>
            <Avatar
              sx={{
                width: 28,
                height: 28,
                mr: 0.75,
                bgcolor: theme.palette.secondary.main,
                boxShadow: '0 0 0 2px rgba(255,255,255,0.2)',
                fontSize: '0.75rem'
              }}
            >
              {currentUser?.username?.charAt(0).toUpperCase() || 'U'}
            </Avatar>
            <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>
              {currentUser?.username}
            </Typography>
          </Box>
          <Button
            color="inherit"
            onClick={handleLogout}
            startIcon={<LogoutIcon fontSize="small" />}
            sx={{
              bgcolor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: 0,
              px: 1.5,
              py: 0.5,
              fontSize: '0.75rem',
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.2)',
              }
            }}
          >
            Logout
          </Button>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default AppHeader; 