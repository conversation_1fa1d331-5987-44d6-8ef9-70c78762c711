import React from 'react';
import { 
  Chip, 
  Tooltip, 
  Box, 
  Typography,
  IconButton,
  Collapse,
  List,
  ListItem,
  ListItemText,
  Badge
} from '@mui/material';
import { 
  Wifi as ConnectedIcon, 
  WifiOff as DisconnectedIcon,
  Refresh as RefreshIcon,
  Notifications as NotificationsIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { useState } from 'react';
import { useWebSocket } from '../hooks/useWebSocket';
import { formatDistanceToNow } from 'date-fns';

interface WebSocketStatusProps {
  showNotifications?: boolean;
  showDetails?: boolean;
  baseUrl?: string;
}

const WebSocketStatus: React.FC<WebSocketStatusProps> = ({ 
  showNotifications = true, 
  showDetails = false,
  baseUrl 
}) => {
  const [expanded, setExpanded] = useState(false);
  const [showNotificationsList, setShowNotificationsList] = useState(false);
  
  const { 
    connected, 
    connect, 
    manifestStatusUpdates, 
    systemNotifications,
    clearManifestUpdates,
    clearSystemNotifications
  } = useWebSocket({ baseUrl });

  const handleReconnect = async () => {
    try {
      await connect();
    } catch (error) {
      console.error('Failed to reconnect:', error);
    }
  };

  const getStatusColor = () => {
    return connected ? 'success' : 'error';
  };

  const getStatusIcon = () => {
    return connected ? <ConnectedIcon /> : <DisconnectedIcon />;
  };

  const getStatusText = () => {
    return connected ? 'Connected' : 'Disconnected';
  };

  const recentUpdates = manifestStatusUpdates.slice(0, 5);
  const recentNotifications = systemNotifications.slice(0, 5);
  const totalNotifications = manifestStatusUpdates.length + systemNotifications.length;

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      {/* Connection Status */}
      <Tooltip title={`WebSocket ${getStatusText()}`}>
        <Chip
          icon={getStatusIcon()}
          label={showDetails ? `Real-time ${getStatusText()}` : ''}
          color={getStatusColor()}
          size="small"
          variant={connected ? 'filled' : 'outlined'}
        />
      </Tooltip>

      {/* Reconnect Button (only when disconnected) */}
      {!connected && (
        <Tooltip title="Reconnect">
          <IconButton size="small" onClick={handleReconnect}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      )}

      {/* Notifications Badge */}
      {showNotifications && totalNotifications > 0 && (
        <Tooltip title={`${totalNotifications} real-time updates`}>
          <IconButton 
            size="small" 
            onClick={() => setShowNotificationsList(!showNotificationsList)}
          >
            <Badge badgeContent={totalNotifications} color="primary" max={99}>
              <NotificationsIcon />
            </Badge>
          </IconButton>
        </Tooltip>
      )}

      {/* Expand/Collapse Details */}
      {showDetails && (
        <IconButton 
          size="small" 
          onClick={() => setExpanded(!expanded)}
        >
          {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </IconButton>
      )}

      {/* Expanded Details */}
      {showDetails && (
        <Collapse in={expanded} sx={{ position: 'absolute', top: '100%', left: 0, zIndex: 1000 }}>
          <Box sx={{ 
            bgcolor: 'background.paper', 
            border: 1, 
            borderColor: 'divider', 
            borderRadius: 1, 
            p: 2, 
            minWidth: 300,
            maxWidth: 400,
            boxShadow: 2
          }}>
            <Typography variant="subtitle2" gutterBottom>
              Real-time Connection Status
            </Typography>
            
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Status: {getStatusText()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Updates received: {manifestStatusUpdates.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Notifications: {systemNotifications.length}
              </Typography>
            </Box>

            {recentUpdates.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Recent Manifest Updates
                </Typography>
                <List dense>
                  {recentUpdates.map((update, index) => (
                    <ListItem key={index} sx={{ py: 0.5 }}>
                      <ListItemText
                        primary={`${update.manifest.trackingNo}: ${update.oldStatus} → ${update.newStatus}`}
                        secondary={`${update.reason} • ${formatDistanceToNow(new Date(update.timestamp))} ago`}
                        primaryTypographyProps={{ variant: 'body2' }}
                        secondaryTypographyProps={{ variant: 'caption' }}
                      />
                    </ListItem>
                  ))}
                </List>
                {manifestStatusUpdates.length > 5 && (
                  <Typography variant="caption" color="text.secondary">
                    ... and {manifestStatusUpdates.length - 5} more
                  </Typography>
                )}
              </Box>
            )}

            {recentNotifications.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Recent System Notifications
                </Typography>
                <List dense>
                  {recentNotifications.map((notification, index) => (
                    <ListItem key={index} sx={{ py: 0.5 }}>
                      <ListItemText
                        primary={notification.message}
                        secondary={`${notification.notificationType} • ${formatDistanceToNow(new Date(notification.timestamp))} ago`}
                        primaryTypographyProps={{ variant: 'body2' }}
                        secondaryTypographyProps={{ variant: 'caption' }}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <IconButton size="small" onClick={clearManifestUpdates} title="Clear manifest updates">
                <Typography variant="caption">Clear Updates</Typography>
              </IconButton>
              <IconButton size="small" onClick={clearSystemNotifications} title="Clear notifications">
                <Typography variant="caption">Clear Notifications</Typography>
              </IconButton>
            </Box>
          </Box>
        </Collapse>
      )}

      {/* Notifications List Popup */}
      {showNotificationsList && (
        <Collapse in={showNotificationsList} sx={{ position: 'absolute', top: '100%', right: 0, zIndex: 1000 }}>
          <Box sx={{ 
            bgcolor: 'background.paper', 
            border: 1, 
            borderColor: 'divider', 
            borderRadius: 1, 
            p: 2, 
            minWidth: 350,
            maxWidth: 500,
            boxShadow: 2,
            maxHeight: 400,
            overflow: 'auto'
          }}>
            <Typography variant="subtitle2" gutterBottom>
              Real-time Updates ({totalNotifications})
            </Typography>
            
            {manifestStatusUpdates.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="primary" gutterBottom>
                  Manifest Status Updates ({manifestStatusUpdates.length})
                </Typography>
                <List dense>
                  {manifestStatusUpdates.slice(0, 10).map((update, index) => (
                    <ListItem key={index} sx={{ py: 0.5, bgcolor: index % 2 === 0 ? 'action.hover' : 'transparent' }}>
                      <ListItemText
                        primary={`${update.manifest.trackingNo}: ${update.oldStatus} → ${update.newStatus}`}
                        secondary={`${update.reason} • ${formatDistanceToNow(new Date(update.timestamp))} ago`}
                        primaryTypographyProps={{ variant: 'body2' }}
                        secondaryTypographyProps={{ variant: 'caption' }}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            {systemNotifications.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="secondary" gutterBottom>
                  System Notifications ({systemNotifications.length})
                </Typography>
                <List dense>
                  {systemNotifications.slice(0, 10).map((notification, index) => (
                    <ListItem key={index} sx={{ py: 0.5, bgcolor: index % 2 === 0 ? 'action.hover' : 'transparent' }}>
                      <ListItemText
                        primary={notification.message}
                        secondary={`${notification.notificationType} • ${formatDistanceToNow(new Date(notification.timestamp))} ago`}
                        primaryTypographyProps={{ variant: 'body2' }}
                        secondaryTypographyProps={{ variant: 'caption' }}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="caption" color="text.secondary">
                Click outside to close
              </Typography>
              <Box>
                <IconButton size="small" onClick={clearManifestUpdates} title="Clear updates">
                  <Typography variant="caption">Clear All</Typography>
                </IconButton>
              </Box>
            </Box>
          </Box>
        </Collapse>
      )}
    </Box>
  );
};

export default WebSocketStatus;
