package com.wms.controller;

import com.wms.dto.ApiResponse;
import com.wms.entity.Manifest;
import com.wms.entity.ManifestStatus;
import com.wms.entity.Container;
import com.wms.security.services.UserDetailsImpl;
import com.wms.service.ManifestService;
import com.wms.service.ContainerService;
import com.wms.service.ManifestStatusService; // Added import for ManifestStatusService
import com.wms.service.WebSocketNotificationService;
import com.wms.repository.ManifestRepository;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Optional;

@RestController
@CrossOrigin(origins = "*")
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT', 'UNSTUFFER')")
public class ManifestController {

    private final ManifestService manifestService;
    private final ContainerService containerService;
    private static final Logger logger = LoggerFactory.getLogger(ManifestController.class);
    private final ManifestStatusService manifestStatusService; // Added field for ManifestStatusService
    private final ManifestRepository manifestRepository; // Added field for ManifestRepository
    private final WebSocketNotificationService webSocketNotificationService; // Added field for WebSocketNotificationService

    @Autowired
    public ManifestController(ManifestService manifestService, ContainerService containerService, ManifestStatusService manifestStatusService, ManifestRepository manifestRepository, WebSocketNotificationService webSocketNotificationService) { // Added webSocketNotificationService to constructor
        this.manifestService = manifestService;
        this.containerService = containerService;
        this.manifestStatusService = manifestStatusService; // Initialize manifestStatusService
        this.manifestRepository = manifestRepository; // Initialize manifestRepository
        this.webSocketNotificationService = webSocketNotificationService; // Initialize webSocketNotificationService
    }

    @GetMapping({"/api/manifests", "/manifests"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'UNSTUFFER')")
    public ResponseEntity<ApiResponse<List<Manifest>>> getAllManifests() {
        List<Manifest> manifests = manifestService.getAllManifests();
        return ResponseEntity.ok(ApiResponse.success(manifests));
    }

    @GetMapping({"/api/manifests/{trackingNo}", "/manifests/{trackingNo}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT', 'UNSTUFFER')")
    public ResponseEntity<ApiResponse<Manifest>> getManifestByTrackingNo(@PathVariable String trackingNo) {
        try {
            Manifest manifest = manifestService.getManifestByTrackingNo(trackingNo)
                    .orElseThrow(() -> new IllegalArgumentException("Manifest not found"));
            
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a client, they can only view their own manifests
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_CLIENT"))) {
                if (!manifest.getClient().getUsername().equals(userDetails.getUsername())) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(ApiResponse.error("You can only view your own manifests"));
                }
            }
            
            return ResponseEntity.ok(ApiResponse.success(manifest));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping({"/api/manifests/my-manifests", "/manifests/my-manifests"})
    @PreAuthorize("hasRole('CLIENT')")
    public ResponseEntity<ApiResponse<List<Manifest>>> getMyManifests() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        
        List<Manifest> manifests = manifestService.getManifestsByUsername(userDetails.getUsername());
        return ResponseEntity.ok(ApiResponse.success(manifests));
    }

    @PostMapping({"/api/manifests", "/manifests"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<Manifest>> createManifest(@Valid @RequestBody Manifest manifest) {
        try {
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a client, they can only create manifests for themselves
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_CLIENT"))) {
                if (!manifest.getClient().getUsername().equals(userDetails.getUsername())) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(ApiResponse.error("You can only create manifests for yourself"));
                }
            }
            
            Manifest createdManifest = manifestService.createManifest(manifest);
            return ResponseEntity.ok(ApiResponse.success("Manifest created successfully", createdManifest));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PostMapping({"/api/manifests/bulk", "/manifests/bulk"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<List<Manifest>>> createManifestsBulk(@Valid @RequestBody List<Manifest> manifests) {
        try {
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a client, they can only create manifests for themselves
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_CLIENT"))) {
                for (Manifest manifest : manifests) {
                    if (!manifest.getClient().getUsername().equals(userDetails.getUsername())) {
                        return ResponseEntity.status(HttpStatus.FORBIDDEN)
                                .body(ApiResponse.error("You can only create manifests for yourself"));
                    }
                }
            }
            
            List<Manifest> createdManifests = manifestService.createManifestsBulk(manifests);
            return ResponseEntity.ok(ApiResponse.success(
                String.format("Successfully created %d manifests", createdManifests.size()), 
                createdManifests));
        } catch (IllegalArgumentException e) {
            logger.error("Bulk manifest creation failed with validation error: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("Bulk manifest creation failed with unexpected error", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("An unexpected error occurred during bulk creation: " + e.getMessage()));
        }
    }

    @PutMapping({"/api/manifests/{trackingNo}", "/manifests/{trackingNo}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<Manifest>> updateManifest(
            @PathVariable String trackingNo,
            @Valid @RequestBody Manifest manifest) {
        try {
            // Get the current manifest
            Manifest existingManifest = manifestService.getManifestByTrackingNo(trackingNo)
                    .orElseThrow(() -> new IllegalArgumentException("Manifest not found"));
            
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a client, they can only update their own manifests
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_CLIENT"))) {
                if (!existingManifest.getClient().getUsername().equals(userDetails.getUsername())) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(ApiResponse.error("You can only update your own manifests"));
                }
            }
            
            // Log the date fields for debugging
            logger.debug("Received manifest update with deliveryDate: {}", manifest.getDeliveryDate());
            logger.debug("Received manifest update with deliveredDate: {}", manifest.getDeliveredDate());
            
            Manifest updatedManifest = manifestService.updateManifest(trackingNo, manifest);
            
            // Log the updated date fields
            logger.debug("Updated manifest with deliveryDate: {}", updatedManifest.getDeliveryDate());
            logger.debug("Updated manifest with deliveredDate: {}", updatedManifest.getDeliveredDate());
            
            return ResponseEntity.ok(ApiResponse.success("Manifest updated successfully", updatedManifest));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @DeleteMapping({"/api/manifests/{trackingNo}", "/manifests/{trackingNo}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Void>> deleteManifest(@PathVariable String trackingNo) {
        try {
            manifestService.deleteManifest(trackingNo);
            return ResponseEntity.ok(ApiResponse.success("Manifest deleted successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/manifests/{trackingNo}/status", "/manifests/{trackingNo}/status"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Manifest>> updateManifestStatus(
            @PathVariable String trackingNo,
            @RequestParam ManifestStatus status) {
        try {
            Manifest updatedManifest = manifestService.updateManifestStatus(trackingNo, status);
            return ResponseEntity.ok(ApiResponse.success("Manifest status updated successfully", updatedManifest));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping({"/api/manifests/count", "/manifests/count"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'UNSTUFFER')")
    public ResponseEntity<ApiResponse<Long>> getManifestsCount() {
        Long count = manifestService.getManifestsCount();
        return ResponseEntity.ok(ApiResponse.success(count));
    }

    @GetMapping({"/api/manifests/by-manifest-no/{manifestNo}", "/manifests/by-manifest-no/{manifestNo}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT', 'UNSTUFFER')")
    public ResponseEntity<ApiResponse<Manifest>> getManifestByManifestNo(@PathVariable String manifestNo) {
        try {
            Manifest manifest = manifestService.getManifestByManifestNo(manifestNo)
                    .orElseThrow(() -> new IllegalArgumentException("Manifest not found"));
            
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a client, they can only view their own manifests
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_CLIENT"))) {
                if (!manifest.getClient().getUsername().equals(userDetails.getUsername())) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(ApiResponse.error("You can only view your own manifests"));
                }
            }
            
            return ResponseEntity.ok(ApiResponse.success(manifest));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping({"/api/manifests/assigned", "/manifests/assigned"})
    @PreAuthorize("hasRole('DRIVER')")
    public ResponseEntity<ApiResponse<List<Manifest>>> getAssignedManifests() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        
        // Get manifests where the driver is the current user
        List<Manifest> manifests = manifestService.getManifestsByDriverUsername(userDetails.getUsername());
        return ResponseEntity.ok(ApiResponse.success(manifests));
    }

    @GetMapping({"/api/manifests/driver/{username}", "/manifests/driver/{username}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER') or #username == authentication.principal.username")
    public ResponseEntity<ApiResponse<List<Manifest>>> getManifestsByDriverUsername(@PathVariable String username) {
        List<Manifest> manifests = manifestService.getManifestsByDriverUsername(username);
        return ResponseEntity.ok(ApiResponse.success(manifests));
    }

    @GetMapping({"/api/manifests/container/{containerNo}", "/manifests/container/{containerNo}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'UNSTUFFER')")
    public ResponseEntity<ApiResponse<List<Manifest>>> getManifestsByContainer(@PathVariable String containerNo) {
        try {
            List<Manifest> manifests = manifestService.getManifestsByContainerNo(containerNo);
            return ResponseEntity.ok(ApiResponse.success(manifests));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error fetching manifests for container: " + e.getMessage()));
        }
    }
    
    /**
     * Debug endpoint to check manifest data initialization
     */
    @GetMapping("/api/debug/manifest-check")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> checkManifestData() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // Check totals
            Long manifestCount = manifestService.getManifestsCount();
            result.put("manifestCount", manifestCount);
            
            // Check manifests by container
            List<Container> containers = containerService.getAllContainers();
            result.put("containerCount", containers.size());
            
            List<Map<String, Object>> containerManifests = new ArrayList<>();
            for (Container container : containers) {
                Map<String, Object> containerData = new HashMap<>();
                containerData.put("containerNo", container.getContainerNo());
                containerData.put("clientUsername", container.getClient().getUsername());
                
                List<Manifest> manifests = manifestService.getManifestsByContainerNo(container.getContainerNo());
                containerData.put("manifestCount", manifests.size());
                
                containerManifests.add(containerData);
            }
            result.put("containerManifests", containerManifests);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error checking manifest data: " + e.getMessage()));
        }
    }

    @GetMapping({"/api/manifests/query", "/manifests/query"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT', 'UNSTUFFER')")
    public ResponseEntity<ApiResponse<Manifest>> getManifestByTrackingNoQuery(@RequestParam String trackingNo) {
        try {
            Manifest manifest = manifestService.getManifestByTrackingNo(trackingNo)
                    .orElseThrow(() -> new IllegalArgumentException("Manifest not found"));
            
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a client, they can only view their own manifests
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_CLIENT"))) {
                if (!manifest.getClient().getUsername().equals(userDetails.getUsername())) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(ApiResponse.error("You can only view your own manifests"));
                }
            }
            
            return ResponseEntity.ok(ApiResponse.success(manifest));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping({"/api/containers/{containerNo}/can-add-manifest", "/containers/{containerNo}/can-add-manifest"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT', 'UNSTUFFER')")
    public ResponseEntity<ApiResponse<Boolean>> canContainerAcceptMoreManifests(@PathVariable String containerNo) {
        try {
            boolean canAcceptMore = manifestService.canContainerAcceptMoreManifests(containerNo);
            return ResponseEntity.ok(ApiResponse.success(canAcceptMore));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/manifests/{trackingNo}/delivery-vehicle", "/manifests/{trackingNo}/delivery-vehicle"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Manifest>> updateManifestDeliveryVehicle(
            @PathVariable String trackingNo,
            @RequestBody Map<String, String> requestBody) {
        try {
            String deliveryVehicle = requestBody.get("deliveryVehicle");
            
            // Get the current manifest
            Manifest existingManifest = manifestService.getManifestByTrackingNo(trackingNo)
                    .orElseThrow(() -> new IllegalArgumentException("Manifest not found"));
            
            logger.debug("Updating delivery vehicle for manifest {} from {} to {}", 
                      trackingNo, existingManifest.getDeliveryVehicle(), deliveryVehicle);
            
            // Create a minimal manifest update with only delivery vehicle change
            Manifest manifestUpdate = new Manifest();
            manifestUpdate.setTrackingNo(existingManifest.getTrackingNo());
            manifestUpdate.setClient(existingManifest.getClient());
            manifestUpdate.setContainer(existingManifest.getContainer());
            manifestUpdate.setDriver(existingManifest.getDriver());
            manifestUpdate.setSequenceNo(existingManifest.getSequenceNo());
            manifestUpdate.setStatus(existingManifest.getStatus());
            manifestUpdate.setCustomerName(existingManifest.getCustomerName());
            manifestUpdate.setPhoneNo(existingManifest.getPhoneNo());
            manifestUpdate.setAddress(existingManifest.getAddress());
            manifestUpdate.setPostalCode(existingManifest.getPostalCode());
            manifestUpdate.setCountry(existingManifest.getCountry());
            manifestUpdate.setPieces(existingManifest.getPieces());
            manifestUpdate.setCbm(existingManifest.getCbm());
            manifestUpdate.setWeight(existingManifest.getWeight());
            manifestUpdate.setLocation(existingManifest.getLocation());
            manifestUpdate.setDeliveryDate(existingManifest.getDeliveryDate());
            manifestUpdate.setDeliveredDate(existingManifest.getDeliveredDate());
            manifestUpdate.setRemarks(existingManifest.getRemarks());
            
            // Set the new delivery vehicle (null or empty string for auto-assignment)
            manifestUpdate.setDeliveryVehicle(deliveryVehicle != null && !deliveryVehicle.trim().isEmpty() ? deliveryVehicle.trim() : null);
            
            Manifest updatedManifest = manifestService.updateManifest(trackingNo, manifestUpdate);
            
            logger.debug("Successfully updated delivery vehicle for manifest {} to {}", 
                      trackingNo, updatedManifest.getDeliveryVehicle());
            
            return ResponseEntity.ok(ApiResponse.success("Delivery vehicle updated successfully", updatedManifest));
        } catch (IllegalArgumentException e) {
            logger.error("Failed to update delivery vehicle for manifest {}: {}", trackingNo, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error updating delivery vehicle for manifest {}", trackingNo, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("An unexpected error occurred: " + e.getMessage()));
        }
    }
    
    @PostMapping({"/api/manifests/update-delivery-statuses", "/manifests/update-delivery-statuses"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Integer>> updateDeliveryStatuses() {
        try {
            int updatedCount = manifestStatusService.updateAllManifestStatusesBasedOnDeliveryDates();
            return ResponseEntity.ok(ApiResponse.success("Successfully updated delivery statuses", updatedCount));
        } catch (Exception e) {
            logger.error("Error updating delivery statuses: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error updating delivery statuses: " + e.getMessage()));
        }
    }

    @GetMapping({"/api/manifests/debug-delivery-dates", "/manifests/debug-delivery-dates"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<java.util.Map<String, Object>>> debugDeliveryDates() {
        try {
            java.time.LocalDate today = java.time.LocalDate.now(java.time.ZoneId.of("Asia/Singapore"));
            java.util.List<ManifestStatus> statusesToCheck = java.util.Arrays.asList(
                ManifestStatus.INBOUNDED_TO_WAREHOUSE,
                ManifestStatus.READY_TO_DELIVER,
                ManifestStatus.PENDING_DELIVER
            );
            java.util.List<Manifest> manifestsWithDates = manifestRepository.findManifestsWithDeliveryDates(statusesToCheck);

            java.util.Map<String, Object> debugInfo = new java.util.HashMap<>();
            debugInfo.put("currentDate", today.toString());
            debugInfo.put("timezone", "Asia/Singapore");
            debugInfo.put("totalManifestsWithDeliveryDates", manifestsWithDates.size());

            java.util.List<java.util.Map<String, Object>> manifestDetails = new java.util.ArrayList<>();
            for (Manifest manifest : manifestsWithDates) {
                if (manifest.getDeliveryDate() != null) {
                    long diffDays = java.time.temporal.ChronoUnit.DAYS.between(today, manifest.getDeliveryDate());
                    java.util.Map<String, Object> details = new java.util.HashMap<>();
                    details.put("trackingNo", manifest.getTrackingNo());
                    details.put("currentStatus", manifest.getStatus().toString());
                    details.put("deliveryDate", manifest.getDeliveryDate().toString());
                    details.put("daysUntilDelivery", diffDays);
                    details.put("shouldBeStatus", diffDays <= 1 ? "READY_TO_DELIVER" : "PENDING_DELIVER");
                    manifestDetails.add(details);
                }
            }
            debugInfo.put("manifests", manifestDetails);

            return ResponseEntity.ok(ApiResponse.success("Debug information retrieved", debugInfo));
        } catch (Exception e) {
            logger.error("Error getting debug delivery dates: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting debug information: " + e.getMessage()));
        }
    }

    @PostMapping({"/api/manifests/bulk-update-delivery-date", "/manifests/bulk-update-delivery-date"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<List<Manifest>>> bulkUpdateDeliveryDate(@RequestBody Map<String, Object> requestBody) {
        try {
            logger.info("Received bulk delivery date update request");
            logger.info("Raw request body: {}", requestBody);
            
            // Extract trackingNos and deliveryDate from request body
            @SuppressWarnings("unchecked")
            List<String> trackingNos = (List<String>) requestBody.get("trackingNos");
            String deliveryDate = (String) requestBody.get("deliveryDate");
            
            logger.info("Request payload - trackingNos: {}, deliveryDate: {}", trackingNos, deliveryDate);
            
            if (trackingNos == null || trackingNos.isEmpty()) {
                return ResponseEntity.badRequest().body(ApiResponse.error("No tracking numbers provided"));
            }
            
            logger.info("Updating delivery date to {} for {} manifests", deliveryDate, trackingNos.size());
            
            List<Manifest> updatedManifests = new ArrayList<>();
            List<String> successfulTrackingNos = new ArrayList<>();
            List<String> failedTrackingNos = new ArrayList<>();
            
            // Process each manifest
            for (String trackingNo : trackingNos) {
                try {
                    logger.info("Processing manifest: {}", trackingNo);
                    // Get the manifest
                    Optional<Manifest> manifestOpt = manifestService.getManifestByTrackingNo(trackingNo);
                    
                    if (!manifestOpt.isPresent()) {
                        logger.error("Manifest not found with tracking number: {}", trackingNo);
                        failedTrackingNos.add(trackingNo);
                        continue;
                    }
                    
                    Manifest manifest = manifestOpt.get();
                    
                    // Parse the delivery date if provided
                    if (deliveryDate != null && !deliveryDate.trim().isEmpty()) {
                        try {
                            // Try to parse the date string to ensure it's valid
                            logger.info("Attempting to parse delivery date: '{}'", deliveryDate);
                            java.time.LocalDate parsedDate;
                            
                            try {
                                // Check if it's a date-only string (no time component)
                                if (deliveryDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                                    // Parse as LocalDate
                                    parsedDate = java.time.LocalDate.parse(deliveryDate);
                                    logger.info("Successfully parsed date-only string: {}", parsedDate);
                                } else if (deliveryDate.contains("T")) {
                                    // Parse ISO format with time and extract just the date part
                                    parsedDate = java.time.LocalDateTime.parse(deliveryDate).toLocalDate();
                                    logger.info("Successfully extracted date from datetime: {}", parsedDate);
                                } else {
                                    // Try with formatter
                                    java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
                                    parsedDate = java.time.LocalDate.parse(deliveryDate, formatter);
                                    logger.info("Successfully parsed with formatter: {}", parsedDate);
                                }
                            } catch (Exception e) {
                                // If parsing fails, try with DateTimeFormatter
                                logger.warn("Failed to parse date with standard methods: {}", e.getMessage());
                                logger.info("Attempting to parse with DateTimeFormatter...");
                                
                                java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
                                parsedDate = java.time.LocalDate.parse(deliveryDate, formatter);
                                logger.info("Successfully parsed as date: {}", parsedDate);
                            }
                            
                            manifest.setDeliveryDate(parsedDate);
                        } catch (Exception e) {
                            logger.error("Failed to parse delivery date '{}': {}", deliveryDate, e.getMessage());
                            logger.error("Date parsing exception details:", e);
                            failedTrackingNos.add(trackingNo);
                            continue;
                        }
                    } else {
                        logger.info("Setting delivery date to null for manifest: {}", trackingNo);
                        manifest.setDeliveryDate(null);
                    }
                    
                    // Update the manifest
                    try {
                    Manifest updatedManifest = manifestService.updateManifest(trackingNo, manifest);
                        logger.info("Successfully updated delivery date for manifest: {}", trackingNo);
                    updatedManifests.add(updatedManifest);
                        successfulTrackingNos.add(trackingNo);
                    } catch (Exception e) {
                        logger.error("Failed to update manifest {}: {}", trackingNo, e.getMessage());
                        failedTrackingNos.add(trackingNo);
                    }
                    
                } catch (Exception e) {
                    logger.error("Error updating delivery date for manifest {}: {}", trackingNo, e.getMessage());
                    failedTrackingNos.add(trackingNo);
                    // Continue with the next manifest instead of failing the entire batch
                }
            }
            
            logger.info("Bulk update completed. Success: {}, Failed: {}", 
                     successfulTrackingNos.size(), failedTrackingNos.size());
            
            if (!successfulTrackingNos.isEmpty()) {
                logger.info("Successfully updated manifests: {}", successfulTrackingNos);
            }
            
            if (!failedTrackingNos.isEmpty()) {
                logger.warn("Failed to update manifests: {}", failedTrackingNos);
            }
            
            String message = String.format(
                "Successfully updated delivery date for %d manifests%s", 
                updatedManifests.size(),
                failedTrackingNos.isEmpty() ? "" : String.format(" (%d failed)", failedTrackingNos.size())
            );
            
            return ResponseEntity.ok(ApiResponse.success(message, updatedManifests));
            
        } catch (Exception e) {
            logger.error("Error in bulk delivery date update", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("An error occurred during bulk delivery date update: " + e.getMessage()));
        }
    }

    @PostMapping({"/api/manifests/debug-time-slot", "/manifests/debug-time-slot"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> debugTimeSlot(@RequestBody Map<String, Object> requestBody) {
        try {
            String trackingNo = (String) requestBody.get("trackingNo");
            String timeSlot = (String) requestBody.get("timeSlot");
            
            logger.info("Debug time slot - trackingNo: {}, timeSlot: {}", trackingNo, timeSlot);
            
            // Get the manifest
            Optional<Manifest> manifestOpt = manifestService.getManifestByTrackingNo(trackingNo);
            if (!manifestOpt.isPresent()) {
                return ResponseEntity.badRequest().body(ApiResponse.error("Manifest not found with tracking number: " + trackingNo));
            }
            
            Manifest manifest = manifestOpt.get();
            
            // Log current time slot
            logger.info("Current time slot for manifest {}: {}", trackingNo, manifest.getTimeSlot());
            
            // Update time slot
            manifest.setTimeSlot(timeSlot);
            
            // Save the manifest
            Manifest updatedManifest = manifestService.updateManifest(trackingNo, manifest);
            
            // Log updated time slot
            logger.info("Updated time slot for manifest {}: {}", trackingNo, updatedManifest.getTimeSlot());
            
            // Return debug info
            Map<String, Object> debugInfo = new HashMap<>();
            debugInfo.put("trackingNo", trackingNo);
            debugInfo.put("requestedTimeSlot", timeSlot);
            debugInfo.put("savedTimeSlot", updatedManifest.getTimeSlot());
            
            return ResponseEntity.ok(ApiResponse.success("Time slot debug completed", debugInfo));
        } catch (Exception e) {
            logger.error("Error in debug time slot endpoint", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error in debug time slot endpoint: " + e.getMessage()));
        }
    }

    @PostMapping({"/api/manifests/test-websocket", "/manifests/test-websocket"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<String>> testWebSocketNotification() {
        try {
            // Send a test system notification
            webSocketNotificationService.broadcastSystemNotification(
                "WebSocket test notification sent at " + java.time.LocalDateTime.now(),
                "info"
            );

            return ResponseEntity.ok(ApiResponse.success("WebSocket test notification sent successfully", "Test notification broadcasted"));
        } catch (Exception e) {
            logger.error("Error sending WebSocket test notification: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error sending WebSocket test notification: " + e.getMessage()));
        }
    }
}