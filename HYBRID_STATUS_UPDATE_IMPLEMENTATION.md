# Hybrid Status Update Implementation Guide

## Quick Wins (Immediate Improvements)

### 1. Increase Scheduling Frequency

**Current:** Daily at midnight
**Recommended:** Every 6 hours

```java
// Change from:
@Scheduled(cron = "0 0 0 * * *") // Daily at midnight

// To:
@Scheduled(cron = "0 0 */6 * * *") // Every 6 hours (00:00, 06:00, 12:00, 18:00)
```

**Benefits:**
- Reduces maximum delay from 24 hours to 6 hours
- Catches delivery date transitions more quickly
- Minimal code change required

### 2. Add Business Hours Scheduling

For even more responsive updates during business hours:

```java
// Additional scheduled task for business hours
@Scheduled(cron = "0 0 8-18/2 * * MON-FRI") // Every 2 hours during business hours, weekdays only
@Transactional
public void businessHoursDeliveryStatusUpdate() {
    logger.info("Starting business hours delivery status update...");
    updateAllManifestStatusesBasedOnDeliveryDates();
}
```

## Event-Driven Implementation

### 1. Create Delivery Date Change Event

```java
// New file: DeliveryDateChangedEvent.java
package com.wms.event;

import com.wms.entity.Manifest;
import org.springframework.context.ApplicationEvent;

public class DeliveryDateChangedEvent extends ApplicationEvent {
    private final Manifest manifest;
    private final java.time.LocalDate oldDeliveryDate;
    private final java.time.LocalDate newDeliveryDate;

    public DeliveryDateChangedEvent(Object source, Manifest manifest, 
                                   java.time.LocalDate oldDeliveryDate, 
                                   java.time.LocalDate newDeliveryDate) {
        super(source);
        this.manifest = manifest;
        this.oldDeliveryDate = oldDeliveryDate;
        this.newDeliveryDate = newDeliveryDate;
    }

    public Manifest getManifest() { return manifest; }
    public java.time.LocalDate getOldDeliveryDate() { return oldDeliveryDate; }
    public java.time.LocalDate getNewDeliveryDate() { return newDeliveryDate; }
}
```

### 2. Add Event Publisher to ManifestService

```java
// In ManifestServiceImpl.java
@Autowired
private ApplicationEventPublisher eventPublisher;

@Override
@Transactional
public Manifest updateManifest(String trackingNo, Manifest manifestDetails) {
    Manifest existingManifest = manifestRepository.findByTrackingNo(trackingNo)
        .orElseThrow(() -> new RuntimeException("Manifest not found: " + trackingNo));

    // Capture old delivery date before update
    java.time.LocalDate oldDeliveryDate = existingManifest.getDeliveryDate();
    
    // Update manifest fields
    existingManifest.setDeliveryDate(manifestDetails.getDeliveryDate());
    // ... other field updates ...
    
    Manifest savedManifest = manifestRepository.save(existingManifest);
    
    // Check if delivery date changed and publish event
    java.time.LocalDate newDeliveryDate = savedManifest.getDeliveryDate();
    if (!Objects.equals(oldDeliveryDate, newDeliveryDate)) {
        logger.info("Delivery date changed for manifest {}: {} -> {}", 
                   trackingNo, oldDeliveryDate, newDeliveryDate);
        
        eventPublisher.publishEvent(new DeliveryDateChangedEvent(
            this, savedManifest, oldDeliveryDate, newDeliveryDate));
    }
    
    return savedManifest;
}
```

### 3. Add Event Listener to ManifestStatusService

```java
// In ManifestStatusServiceImpl.java
@EventListener
@Async // Process asynchronously to avoid blocking the update operation
@Transactional
public void handleDeliveryDateChange(DeliveryDateChangedEvent event) {
    try {
        Manifest manifest = event.getManifest();
        logger.info("Processing delivery date change event for manifest {}: {} -> {}", 
                   manifest.getTrackingNo(), 
                   event.getOldDeliveryDate(), 
                   event.getNewDeliveryDate());
        
        // Only process if manifest has a delivery date and is in relevant status
        if (manifest.getDeliveryDate() != null && 
            (manifest.getStatus() == ManifestStatus.INBOUNDED_TO_WAREHOUSE ||
             manifest.getStatus() == ManifestStatus.PENDING_DELIVER ||
             manifest.getStatus() == ManifestStatus.READY_TO_DELIVER)) {
            
            ManifestStatus originalStatus = manifest.getStatus();
            Manifest updatedManifest = checkAndUpdateStatusBasedOnDeliveryDate(manifest);
            
            if (!originalStatus.equals(updatedManifest.getStatus())) {
                logger.info("Event-driven status update: Manifest {} changed from {} to {} due to delivery date change", 
                           updatedManifest.getTrackingNo(), originalStatus, updatedManifest.getStatus());
            }
        }
    } catch (Exception e) {
        logger.error("Error processing delivery date change event for manifest {}: {}", 
                    event.getManifest().getTrackingNo(), e.getMessage(), e);
    }
}
```

### 4. Enable Async Processing

```java
// New file: AsyncConfig.java
package com.wms.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Configuration
@EnableAsync
public class AsyncConfig {

    @Bean(name = "eventTaskExecutor")
    public Executor eventTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("EventAsync-");
        executor.initialize();
        return executor;
    }
}
```

## Lazy Evaluation for Critical Views

### Add Status Check to Manifest Retrieval

```java
// In ManifestService.java - add new method
public Manifest getManifestWithCurrentStatus(String trackingNo) {
    Manifest manifest = getManifestByTrackingNo(trackingNo);
    
    // Always ensure status is current when specifically requested
    if (manifest.getDeliveryDate() != null) {
        manifest = manifestStatusService.checkAndUpdateStatusBasedOnDeliveryDate(manifest);
    }
    
    return manifest;
}

// In ManifestController.java - use for detail views
@GetMapping({"/api/manifests/{trackingNo}", "/manifests/{trackingNo}"})
public ResponseEntity<ApiResponse<Manifest>> getManifest(@PathVariable String trackingNo) {
    try {
        // Use the status-checking version for detail views
        Manifest manifest = manifestService.getManifestWithCurrentStatus(trackingNo);
        return ResponseEntity.ok(ApiResponse.success(manifest));
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("Manifest not found: " + trackingNo));
    }
}
```

## Configuration-Based Scheduling

### Make Scheduling Configurable

```java
// In application.properties
wms.scheduling.delivery-status.enabled=true
wms.scheduling.delivery-status.cron=0 0 */6 * * *
wms.scheduling.delivery-status.business-hours-enabled=true
wms.scheduling.delivery-status.business-hours-cron=0 0 8-18/2 * * MON-FRI

// In ManifestStatusServiceImpl.java
@Scheduled(cron = "${wms.scheduling.delivery-status.cron:0 0 0 * * *}")
@ConditionalOnProperty(name = "wms.scheduling.delivery-status.enabled", havingValue = "true", matchIfMissing = true)
@Transactional
public void scheduledDeliveryDateStatusUpdate() {
    // existing implementation
}

@Scheduled(cron = "${wms.scheduling.delivery-status.business-hours-cron:0 0 8-18/2 * * MON-FRI}")
@ConditionalOnProperty(name = "wms.scheduling.delivery-status.business-hours-enabled", havingValue = "true")
@Transactional
public void businessHoursDeliveryStatusUpdate() {
    logger.info("Starting business hours delivery status update...");
    updateAllManifestStatusesBasedOnDeliveryDates();
}
```

## Implementation Priority

### Phase 1: Quick Wins (1-2 hours)
1. ✅ **Already done:** Fix timezone and logging issues
2. Change scheduling frequency to every 6 hours
3. Add configurable scheduling

### Phase 2: Event-Driven (4-6 hours)
1. Create `DeliveryDateChangedEvent` class
2. Add event publishing to `ManifestService.updateManifest()`
3. Add event listener to `ManifestStatusService`
4. Configure async processing

### Phase 3: Enhanced Features (2-4 hours)
1. Add lazy evaluation for detail views
2. Add business hours scheduling
3. Enhanced monitoring and metrics

### Phase 4: Advanced Features (Optional)
1. WebSocket notifications for real-time UI updates
2. Metrics and monitoring dashboard
3. Performance optimization

## Testing Strategy

### 1. Unit Tests
```java
@Test
public void testDeliveryDateChangeEvent() {
    // Test event publishing when delivery date changes
}

@Test
public void testEventListener() {
    // Test event listener processes status updates correctly
}
```

### 2. Integration Tests
```java
@Test
public void testHybridStatusUpdate() {
    // Test that both scheduled and event-driven updates work
}
```

### 3. Manual Testing
1. Use debug endpoint to verify current state
2. Change delivery dates and verify immediate updates
3. Wait for scheduled updates and verify fallback works

This hybrid approach provides the best balance of immediate responsiveness and reliable fallback processing.
