import { Client, IMessage } from '@stomp/stompjs';
import SockJS from 'sockjs-client';

export interface ManifestStatusUpdate {
  type: 'manifest_status_update';
  timestamp: string;
  reason: string;
  oldStatus: string | null;
  newStatus: string;
  manifest: {
    trackingNo: string;
    status: string;
    pieces: number;
    inboundPieces: number;
    deliveryDate: string | null;
    actualPalletsCount: number;
    container?: {
      containerNo: string;
      status: string;
    };
  };
}

export interface SystemNotification {
  type: 'system_notification';
  message: string;
  notificationType: 'info' | 'warning' | 'error' | 'success';
  timestamp: string;
}

export type WebSocketMessage = ManifestStatusUpdate | SystemNotification;

export class WebSocketService {
  private client: Client | null = null;
  private connected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private maxReconnectDelay = 30000; // Max 30 seconds
  
  // Event listeners
  private manifestStatusListeners: ((update: ManifestStatusUpdate) => void)[] = [];
  private systemNotificationListeners: ((notification: SystemNotification) => void)[] = [];
  private connectionListeners: ((connected: boolean) => void)[] = [];

  constructor(private baseUrl: string = 'http://localhost:8080') {
    this.setupClient();
  }

  private setupClient() {
    // Create WebSocket connection using SockJS for better compatibility
    const socket = new SockJS(`${this.baseUrl}/ws`);
    
    this.client = new Client({
      webSocketFactory: () => socket,
      debug: (str) => {
        console.log('STOMP Debug:', str);
      },
      reconnectDelay: this.reconnectDelay,
      heartbeatIncoming: 4000,
      heartbeatOutgoing: 4000,
      onConnect: (frame) => {
        console.log('WebSocket connected:', frame);
        this.connected = true;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000; // Reset delay
        this.notifyConnectionListeners(true);
        this.subscribeToTopics();
      },
      onDisconnect: (frame) => {
        console.log('WebSocket disconnected:', frame);
        this.connected = false;
        this.notifyConnectionListeners(false);
      },
      onStompError: (frame) => {
        console.error('STOMP error:', frame);
        this.connected = false;
        this.notifyConnectionListeners(false);
        this.handleReconnect();
      },
      onWebSocketError: (error) => {
        console.error('WebSocket error:', error);
        this.connected = false;
        this.notifyConnectionListeners(false);
        this.handleReconnect();
      }
    });
  }

  private subscribeToTopics() {
    if (!this.client || !this.connected) return;

    // Subscribe to manifest status updates
    this.client.subscribe('/topic/manifest-status', (message: IMessage) => {
      try {
        const update: ManifestStatusUpdate = JSON.parse(message.body);
        console.log('Received manifest status update:', update);
        this.notifyManifestStatusListeners(update);
      } catch (error) {
        console.error('Error parsing manifest status update:', error);
      }
    });

    // Subscribe to system notifications
    this.client.subscribe('/topic/system-notifications', (message: IMessage) => {
      try {
        const notification: SystemNotification = JSON.parse(message.body);
        console.log('Received system notification:', notification);
        this.notifySystemNotificationListeners(notification);
      } catch (error) {
        console.error('Error parsing system notification:', error);
      }
    });

    console.log('Subscribed to WebSocket topics');
  }

  private handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached. Giving up.');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      if (!this.connected) {
        this.connect();
      }
    }, delay);
  }

  public connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.connected) {
        resolve();
        return;
      }

      if (!this.client) {
        this.setupClient();
      }

      const connectListener = (connected: boolean) => {
        if (connected) {
          this.removeConnectionListener(connectListener);
          resolve();
        }
      };

      this.addConnectionListener(connectListener);

      try {
        this.client!.activate();
      } catch (error) {
        this.removeConnectionListener(connectListener);
        reject(error);
      }
    });
  }

  public disconnect(): void {
    if (this.client) {
      this.client.deactivate();
      this.client = null;
    }
    this.connected = false;
    this.notifyConnectionListeners(false);
  }

  public isConnected(): boolean {
    return this.connected;
  }

  // Subscription methods for container-specific updates
  public subscribeToContainer(containerNo: string): void {
    if (!this.client || !this.connected) return;

    this.client.subscribe(`/topic/container/${containerNo}/manifests`, (message: IMessage) => {
      try {
        const update: ManifestStatusUpdate = JSON.parse(message.body);
        console.log(`Received container ${containerNo} manifest update:`, update);
        this.notifyManifestStatusListeners(update);
      } catch (error) {
        console.error('Error parsing container manifest update:', error);
      }
    });
  }

  // Event listener management
  public addManifestStatusListener(listener: (update: ManifestStatusUpdate) => void): void {
    this.manifestStatusListeners.push(listener);
  }

  public removeManifestStatusListener(listener: (update: ManifestStatusUpdate) => void): void {
    const index = this.manifestStatusListeners.indexOf(listener);
    if (index > -1) {
      this.manifestStatusListeners.splice(index, 1);
    }
  }

  public addSystemNotificationListener(listener: (notification: SystemNotification) => void): void {
    this.systemNotificationListeners.push(listener);
  }

  public removeSystemNotificationListener(listener: (notification: SystemNotification) => void): void {
    const index = this.systemNotificationListeners.indexOf(listener);
    if (index > -1) {
      this.systemNotificationListeners.splice(index, 1);
    }
  }

  public addConnectionListener(listener: (connected: boolean) => void): void {
    this.connectionListeners.push(listener);
  }

  public removeConnectionListener(listener: (connected: boolean) => void): void {
    const index = this.connectionListeners.indexOf(listener);
    if (index > -1) {
      this.connectionListeners.splice(index, 1);
    }
  }

  // Notification methods
  private notifyManifestStatusListeners(update: ManifestStatusUpdate): void {
    this.manifestStatusListeners.forEach(listener => {
      try {
        listener(update);
      } catch (error) {
        console.error('Error in manifest status listener:', error);
      }
    });
  }

  private notifySystemNotificationListeners(notification: SystemNotification): void {
    this.systemNotificationListeners.forEach(listener => {
      try {
        listener(notification);
      } catch (error) {
        console.error('Error in system notification listener:', error);
      }
    });
  }

  private notifyConnectionListeners(connected: boolean): void {
    this.connectionListeners.forEach(listener => {
      try {
        listener(connected);
      } catch (error) {
        console.error('Error in connection listener:', error);
      }
    });
  }
}

// Singleton instance
let webSocketService: WebSocketService | null = null;

export const getWebSocketService = (baseUrl?: string): WebSocketService => {
  if (!webSocketService) {
    webSocketService = new WebSocketService(baseUrl);
  }
  return webSocketService;
};

export default WebSocketService;
