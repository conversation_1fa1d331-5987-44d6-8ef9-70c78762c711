import React, { useEffect, useState, useRef, Suspense } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  LinearProgress,
  IconButton,
  Autocomplete
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import ContainerList from '../components/ContainerList';
import usePageTitle from '../hooks/usePageTitle';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../contexts/ToastContext';
import CustomDatePickerWithCounts from '../components/CustomDatePickerWithCounts';
import containerService from '../services/container.service';
import userService from '../services/user.service';
import { Container, ContainerStatus } from '../types/Container';
import { Client } from '../types/User';
import { ContainerFormData } from '../components/ContainerList/types';



// Enhanced loading fallback component with modern styling
const LoadingFallback = () => (
  <Box sx={{
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '400px',
    width: '100%',
    bgcolor: 'background.default',
    borderRadius: 2,
    border: '1px solid',
    borderColor: 'divider'
  }}>
    <Box sx={{
      p: 4,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 2
    }}>
      <CircularProgress
        size={48}
        thickness={4}
        sx={{ color: 'primary.main' }}
      />
      <Typography
        variant="h6"
        color="text.primary"
        sx={{ fontWeight: 500 }}
      >
        Loading Container Management
      </Typography>
      <Typography
        variant="body2"
        color="text.secondary"
        sx={{ textAlign: 'center', maxWidth: 300 }}
      >
        Please wait while we fetch your container data...
      </Typography>
    </Box>
  </Box>
);

/**
 * ContainerManagementTest Page Component
 * 
 * This page provides a comprehensive container management interface with:
 * - Container listing with advanced filtering and search
 * - Container creation and editing capabilities
 * - Bulk operations and Excel import/export
 * - Real-time updates and status management
 * - Professional UI following ManifestListTest design patterns
 */
const ContainerManagementTest: React.FC = () => {
  usePageTitle('Container Management');
  
  const { currentUser } = useAuth();
  const toast = useToast();
  
  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const firstInputRef = useRef<HTMLInputElement>(null);
  
  // State management
  const [resourcesLoading, setResourcesLoading] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);

  // Dialog states
  const [createContainerDialogOpen, setCreateContainerDialogOpen] = useState(false);

  // Form states
  const [formData, setFormData] = useState<ContainerFormData>({
    containerNo: '',
    clientUsername: '',
    truckNo: '',
    vesselVoyageNo: '',
    etaRequestedDate: null,
    manifestQuantity: 0,
    portnetEta: null,
    etaAllocated: null,
    arrivalDate: null,
    loadingBay: '',
    unstuffDate: null,
    unstuffCompletedDate: null,
    pullOutDate: null,
    unstuffTeam: '',
    remark: '',
    status: ContainerStatus.CREATED
  });

  const [formLoading, setFormLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  
  // Load clients
  const loadClients = async () => {
    try {
      setResourcesLoading(true);
      const response = await userService.getClients(currentUser?.token || '');
      if (response.success && response.data) {
        setClients(response.data);
      }
    } catch (error) {
      console.error('Error loading clients:', error);
      toast.error('Failed to load clients');
    } finally {
      setResourcesLoading(false);
    }
  };
  
  // Container management handlers
  const handleOpenCreateContainer = () => {
    setCreateContainerDialogOpen(true);
    setFormError(null);
  };
  
  const handleCloseCreateContainer = () => {
    setCreateContainerDialogOpen(false);
    setFormData({
      containerNo: '',
      clientUsername: '',
      truckNo: '',
      vesselVoyageNo: '',
      etaRequestedDate: null,
      manifestQuantity: 0,
      portnetEta: null,
      etaAllocated: null,
      arrivalDate: null,
      loadingBay: '',
      unstuffDate: null,
      unstuffCompletedDate: null,
      pullOutDate: null,
      unstuffTeam: '',
      remark: '',
      status: ContainerStatus.CREATED
    });
    setFormError(null);
  };
  

  
  // Container deletion handlers
  const handleContainerDelete = async (containerNo: string) => {
    try {
      const response = await containerService.deleteContainer(containerNo, currentUser?.token || '');
      if (response.success) {
        toast.success('Container deleted successfully');

        // Emit event for real-time updates
        const containerDeletedEvent = new CustomEvent('container-deleted', {
          detail: {
            type: 'deleted',
            containerNo: containerNo,
            timestamp: new Date().toISOString()
          }
        });
        window.dispatchEvent(containerDeletedEvent);
      } else {
        toast.error(response.message || 'Failed to delete container');
      }
    } catch (error: any) {
      console.error('Error deleting container:', error);
      toast.error(error.response?.data?.message || 'Failed to delete container');
    }
  };
  
  const handleBulkContainerDelete = async (containerNos: string[]) => {
    try {
      // For now, simulate bulk delete since we may not have the service method
      for (const containerNo of containerNos) {
        await containerService.deleteContainer(containerNo, currentUser?.token || '');
      }

      toast.success(`${containerNos.length} containers deleted successfully`);

      // Emit event for real-time updates
      const bulkDeleteEvent = new CustomEvent('containers-bulk-deleted', {
        detail: {
          type: 'bulk-deleted',
          containerNos: containerNos,
          timestamp: new Date().toISOString()
        }
      });
      window.dispatchEvent(bulkDeleteEvent);
    } catch (error: any) {
      console.error('Error bulk deleting containers:', error);
      toast.error(error.response?.data?.message || 'Failed to delete containers');
    }
  };

  // Handle container creation
  const handleCreateContainer = async () => {
    try {
      setFormLoading(true);
      setFormError(null);

      // Find the selected client
      const selectedClient = clients.find(c => c.username === formData.clientUsername);
      if (!selectedClient) {
        setFormError('Please select a valid client');
        return;
      }

      // Transform form data to Container format
      const containerData: Container = {
        containerNo: formData.containerNo,
        client: selectedClient,
        truckNo: formData.truckNo,
        vesselVoyageNo: formData.vesselVoyageNo,
        etaRequestedDate: formData.etaRequestedDate?.toISOString() || '',
        manifestQuantity: formData.manifestQuantity,
        portnetEta: formData.portnetEta?.toISOString(),
        etaAllocated: formData.etaAllocated?.toISOString(),
        arrivalDate: formData.arrivalDate?.toISOString(),
        loadingBay: formData.loadingBay,
        unstuffDate: formData.unstuffDate?.toISOString(),
        unstuffCompletedDate: formData.unstuffCompletedDate?.toISOString(),
        pullOutDate: formData.pullOutDate?.toISOString(),
        unstuffTeam: formData.unstuffTeam,
        remark: formData.remark,
        status: formData.status
      };

      const response = await containerService.createContainer(containerData, currentUser?.token || '');
      if (response.success) {
        toast.success('Container created successfully');
        handleCloseCreateContainer();

        // Emit event for real-time updates
        const containerCreatedEvent = new CustomEvent('container-created', {
          detail: {
            type: 'created',
            container: response.data,
            timestamp: new Date().toISOString()
          }
        });
        window.dispatchEvent(containerCreatedEvent);
      } else {
        setFormError(response.message || 'Failed to create container');
      }
    } catch (error: any) {
      console.error('Error creating container:', error);
      setFormError(error.response?.data?.message || 'Failed to create container');
    } finally {
      setFormLoading(false);
    }
  };

  // Handle form field changes
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle client selection change
  const handleClientChange = (clientUsername: string) => {
    setFormData(prev => ({
      ...prev,
      clientUsername: clientUsername,
      // Reset dependent fields when client changes
      containerNo: '',
      truckNo: '',
      vesselVoyageNo: ''
    }));
  };
  
  // Load resources on component mount
  useEffect(() => {
    console.log('Loading resources on component mount, currentUser:', currentUser?.username);
    loadClients();
  }, [currentUser]);
  
  // Focus management for dialog
  useEffect(() => {
    if (createContainerDialogOpen && firstInputRef.current) {
      setTimeout(() => {
        const rootElement = document.getElementById('root');
        if (rootElement && rootElement.getAttribute('aria-hidden') === 'true') {
          rootElement.removeAttribute('aria-hidden');
        }
        firstInputRef.current?.focus();
      }, 150);
    }
  }, [createContainerDialogOpen]);
  

  
  return (
    <Box
      ref={containerRef}
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        p: 0 // Remove padding
      }}
    >
      {/* The ContainerList component will fetch data from the API */}
      <Box sx={{
        flexGrow: 1,
        width: '100%',
        px: 0 // Remove padding
      }}>
        <Suspense fallback={<LoadingFallback />}>
          <ContainerList
            onContainerDelete={handleContainerDelete}
            onBulkDelete={handleBulkContainerDelete}
            onCreateContainer={handleOpenCreateContainer}
          />
        </Suspense>

        {/* Create Container Dialog - Matching ManifestListTest Style */}
        <Dialog
          open={createContainerDialogOpen}
          onClose={handleCloseCreateContainer}
          maxWidth="lg"
          fullWidth
          disableEscapeKeyDown={false}
          disableRestoreFocus={false}
          keepMounted={false}
          aria-labelledby="create-container-dialog-title"
          aria-describedby="create-container-dialog-description"
          sx={{
            '& .MuiDialog-paper': {
              borderRadius: 3,
              boxShadow: 24,
              position: 'relative',
              zIndex: 1300
            },
            '& .MuiBackdrop-root': {
              zIndex: 1299
            }
          }}
          slotProps={{
            backdrop: {
              sx: {
                zIndex: 1299
              }
            }
          }}
        >
          <DialogTitle sx={{
            bgcolor: 'primary.main',
            color: 'white',
            px: 3,
            py: 2
          }}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="h6" id="create-container-dialog-title">
                Create Container
              </Typography>
              <IconButton
                aria-label="close"
                onClick={handleCloseCreateContainer}
                sx={{
                  color: 'white',
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>

          <DialogContent dividers sx={{ px: 3, py: 3, '&::-webkit-scrollbar': { width: 8 }, '&::-webkit-scrollbar-thumb': { backgroundColor: 'rgba(0,0,0,0.2)', borderRadius: 4 } }}>
            {/* Hidden description for screen readers */}
            <Typography id="create-container-dialog-description" sx={{ display: 'none' }}>
              Create a new container by filling out the required information including container number, client, truck number, and vessel details.
            </Typography>

            {formError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {formError}
              </Alert>
            )}

            {resourcesLoading && <LinearProgress sx={{ mb: 2 }} />}

            <form>
              <Box sx={{
                display: 'grid',
                gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                gap: 2,
                mb: 2
              }}>
                {/* Container Number */}
                <TextField
                  ref={firstInputRef}
                  label="Container Number"
                  value={formData.containerNo}
                  onChange={(e) => handleFormChange('containerNo', e.target.value)}
                  fullWidth
                  required
                  margin="normal"
                />

                {/* Client Selection */}
                <Autocomplete
                  options={clients}
                  getOptionLabel={(option) => option.companyName || option.username}
                  value={clients.find(c => c.username === formData.clientUsername) || null}
                  onChange={(_, value) => handleClientChange(value?.username || '')}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Client"
                      required
                      margin="normal"
                    />
                  )}
                  loading={resourcesLoading}
                />

                {/* Truck Number */}
                <TextField
                  label="Truck Number"
                  value={formData.truckNo}
                  onChange={(e) => handleFormChange('truckNo', e.target.value)}
                  fullWidth
                  required
                  margin="normal"
                />

                {/* Vessel/Voyage Number */}
                <TextField
                  label="Vessel/Voyage Number"
                  value={formData.vesselVoyageNo}
                  onChange={(e) => handleFormChange('vesselVoyageNo', e.target.value)}
                  fullWidth
                  required
                  margin="normal"
                />

                {/* Manifest Quantity */}
                <TextField
                  label="Manifest Quantity"
                  type="number"
                  value={formData.manifestQuantity}
                  onChange={(e) => handleFormChange('manifestQuantity', parseInt(e.target.value) || 0)}
                  fullWidth
                  required
                  margin="normal"
                  slotProps={{
                    htmlInput: { min: 0 }
                  }}
                />

                {/* Loading Bay */}
                <TextField
                  label="Loading Bay"
                  value={formData.loadingBay}
                  onChange={(e) => handleFormChange('loadingBay', e.target.value)}
                  fullWidth
                  margin="normal"
                />

                {/* ETA Requested Date */}
                <Box position="relative" width="100%">
                  <CustomDatePickerWithCounts
                    label="ETA Requested Date"
                    value={formData.etaRequestedDate}
                    onChange={(newValue) => handleFormChange('etaRequestedDate', newValue)}
                    containers={[]}
                    slotProps={{
                      popper: {
                        sx: {
                          zIndex: 10001,
                        }
                      }
                    }}
                  />
                </Box>

                {/* Unstuff Team */}
                <TextField
                  label="Unstuff Team"
                  value={formData.unstuffTeam}
                  onChange={(e) => handleFormChange('unstuffTeam', e.target.value)}
                  fullWidth
                  margin="normal"
                />

                {/* Remarks */}
                <TextField
                  label="Remarks"
                  value={formData.remark}
                  onChange={(e) => handleFormChange('remark', e.target.value)}
                  fullWidth
                  multiline
                  rows={3}
                  margin="normal"
                  sx={{ gridColumn: 'span 2' }}
                />
              </Box>
            </form>
          </DialogContent>

          <DialogActions sx={{ p: 3, bgcolor: 'grey.50' }}>
            <Button
              onClick={handleCloseCreateContainer}
              variant="outlined"
              sx={{ borderRadius: 2 }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateContainer}
              variant="contained"
              color="primary"
              disabled={formLoading || !formData.containerNo || !formData.clientUsername || !formData.truckNo}
              startIcon={formLoading ? <CircularProgress size={20} /> : <AddIcon />}
              sx={{ borderRadius: 2 }}
            >
              {formLoading ? 'Creating...' : 'Create Container'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Box>
  );
};

export default ContainerManagementTest;
