# Manifest Status Auto-Update System

## Overview

The WMS system automatically updates manifest statuses based on two main criteria:
1. **Pallet piece counts** - Immediate updates when pallets are added/modified
2. **Delivery dates** - Scheduled and event-driven updates based on delivery proximity

## Automatic Status Update Mechanisms

### 1. Pallet-Based Status Updates (Immediate)

The system automatically checks and updates manifest status whenever:
- Pallets are added to a manifest
- Pallets are updated (piece count changes)
- Pallets are deleted from a manifest
- Manifest pieces count is changed

**Status Logic:**
```
Total Pallet Pieces > Manifest Pieces → DISCREPANCY
Total Pallet Pieces = Manifest Pieces → INBOUNDED_TO_WAREHOUSE
Total Pallet Pieces < Manifest Pieces → INBOUNDING
No Pallets → Remains in current status
```

**Triggered by:**
- `PalletService.createPallet()`
- `PalletService.updatePallet()`
- `PalletService.deletePallet()`
- `PalletService.createPalletsBulk()`
- `ManifestService.updateManifest()` (when pieces change)

### 2. Delivery Date-Based Status Updates

#### Immediate Updates (Event-Driven)
When a manifest reaches `INBOUNDED_TO_WAREHOUSE` status and has a delivery date, the system immediately checks:
- **≤ 1 day until delivery** → `READY_TO_DELIVER`
- **≥ 2 days until delivery** → `PENDING_DELIVER`

#### Scheduled Updates (Daily at Midnight)
A scheduled task runs daily at midnight (`@Scheduled(cron = "0 0 0 * * *")`) to:
1. Find all manifests with delivery dates in statuses:
   - `INBOUNDED_TO_WAREHOUSE`
   - `READY_TO_DELIVER`
   - `PENDING_DELIVER`
2. Check each manifest's delivery date proximity
3. Update status accordingly

#### Manual Trigger
Administrators can manually trigger delivery status updates via API:
```
POST /api/manifests/update-delivery-statuses
```

## Status Transition Rules

### From CREATED
- Can transition to `INBOUNDING` when first pallet is added
- Can transition to `INBOUNDED_TO_WAREHOUSE` if pallets exactly match manifest pieces
- Can transition to `DISCREPANCY` if pallets exceed manifest pieces

### From INBOUNDING
- Can transition to `INBOUNDED_TO_WAREHOUSE` when pallet pieces = manifest pieces
- Can transition to `DISCREPANCY` if pallet pieces > manifest pieces

### From INBOUNDED_TO_WAREHOUSE
- Can transition to `READY_TO_DELIVER` if delivery date ≤ 1 day away
- Can transition to `PENDING_DELIVER` if delivery date ≥ 2 days away
- Can revert to `INBOUNDING` if pallets are removed

### From READY_TO_DELIVER
- Can transition to `PENDING_DELIVER` if delivery date moves to ≥ 2 days away
- Can transition to `DELIVERING` (manual)
- Can transition to `DELIVERED` (manual)

### From PENDING_DELIVER
- Can transition to `READY_TO_DELIVER` if delivery date moves to ≤ 1 day away

### From DISCREPANCY
- Requires manual intervention to resolve
- Can transition to appropriate status once pallet/manifest pieces are corrected

## Key Service Methods

### ManifestStatusService
- `checkAndUpdateManifestStatus(Manifest manifest)` - Main status check method
- `checkAndUpdateStatusBasedOnDeliveryDate(Manifest manifest)` - Delivery date logic
- `scheduledDeliveryDateStatusUpdate()` - Daily scheduled task
- `updateAllManifestStatusesBasedOnDeliveryDates()` - Manual trigger

### Integration Points
- **PalletServiceImpl** - Calls status check after pallet operations
- **ManifestServiceImpl** - Calls status check after manifest updates
- **Scheduled Tasks** - Daily midnight execution
- **REST API** - Manual trigger endpoint

## Logging and Tracking

All automatic status changes are logged to the manifest tracking history with:
- Original status
- New status
- Reason for change
- Timestamp
- User context (system for automatic updates)

**Log Messages:**
- `"Status auto-updated based on pallet pieces"`
- `"Status auto-updated based on delivery date"`

## Configuration

### Delivery Date Thresholds
Currently hardcoded in `ManifestStatusServiceImpl`:
- **Ready threshold**: ≤ 1 day
- **Pending threshold**: ≥ 2 days

### Scheduled Task Timing
- **Current**: Daily at midnight (`0 0 0 * * *`)
- **Alternatives** (commented in code):
  - Every 6 hours: `0 0 6 * * *`
  - Twice daily: `0 0 8,20 * * *`
  - Every hour: `fixedRate = 3600000`

## Status Update Flow Summary

1. **Event occurs** (pallet operation, manifest update, scheduled task)
2. **System calculates** current state (pallet pieces, delivery date proximity)
3. **Status determined** based on business rules
4. **Database updated** if status changed
5. **Change logged** to tracking history
6. **Process completes** with updated manifest returned

This automatic system ensures manifest statuses accurately reflect the current state of inventory and delivery schedules without requiring manual intervention.
