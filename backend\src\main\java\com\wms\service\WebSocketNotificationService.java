package com.wms.service;

import com.wms.entity.Manifest;
import com.wms.entity.ManifestStatus;

/**
 * Service for sending real-time notifications via WebSocket
 */
public interface WebSocketNotificationService {
    
    /**
     * Broadcast manifest status update to all connected clients
     * 
     * @param manifest The manifest that was updated
     * @param oldStatus The previous status
     * @param newStatus The new status
     * @param reason The reason for the status change
     */
    void broadcastManifestStatusUpdate(Manifest manifest, ManifestStatus oldStatus, ManifestStatus newStatus, String reason);
    
    /**
     * Send manifest status update to specific user
     * 
     * @param username The username to send the update to
     * @param manifest The manifest that was updated
     * @param oldStatus The previous status
     * @param newStatus The new status
     * @param reason The reason for the status change
     */
    void sendManifestStatusUpdateToUser(String username, Manifest manifest, ManifestStatus oldStatus, ManifestStatus newStatus, String reason);
    
    /**
     * Broadcast container-related manifest updates
     * 
     * @param containerNo The container number
     * @param manifest The manifest that was updated
     * @param oldStatus The previous status
     * @param newStatus The new status
     * @param reason The reason for the status change
     */
    void broadcastContainerManifestUpdate(String containerNo, Manifest manifest, ManifestStatus oldStatus, ManifestStatus newStatus, String reason);
    
    /**
     * Send system notification to all connected clients
     * 
     * @param message The notification message
     * @param type The notification type (info, warning, error, success)
     */
    void broadcastSystemNotification(String message, String type);
}
